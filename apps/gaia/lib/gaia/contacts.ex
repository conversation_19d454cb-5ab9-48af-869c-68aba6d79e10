defmodule Gaia.Contacts do
  @moduledoc """
  The Contacts context.
  """
  use Helper.Pipe

  import Ecto.Query, warn: false

  alias <PERSON><PERSON>.BeneficialOwners.Account
  alias Gaia.Comms.ContactGlobalUnsubscribe
  alias Gaia.Comms.ContactSuppression
  alias Gaia.Comms.ContactUnsubscribe
  alias Gaia.Comms.EmailRecipient
  alias Gaia.Companies.Profile
  alias Gaia.Contacts.Contact
  alias Gaia.Contacts.DynamicList
  alias Gaia.Contacts.EmailLog
  alias Gaia.Contacts.Note
  alias Gaia.Contacts.StaticList
  alias Gaia.Contacts.StaticListMember
  alias Gaia.Contacts.Tag
  alias Gaia.FeatureFlags
  alias Gaia.Investors.User
  alias Gaia.Registers.Shareholding
  alias Gaia.Repo
  alias Gaia.Tracking.EmailEvent
  alias Gaia.Uploaders.Companies.EmailNames

  require Logger

  @doc """
  Returns a map of id to %Contact{}

  ## Examples

      iex> batch_get_contacts(%{company_profile_id: 1}, [[1, 2], [2, 3]])
      [%Contact{}, ...]

  """
  def batch_get_contacts(%{company_profile_id: company_profile_id}, list_of_contact_ids) do
    unique_contact_ids =
      list_of_contact_ids
      |> List.flatten()
      |> Enum.uniq()

    Contact
    |> where(company_profile_id: ^company_profile_id)
    |> where([c], c.id in ^unique_contact_ids)
    |> Repo.all(with_invalidated: true)
    |> Map.new(fn c -> {c.id, c} end)
  end

  @doc """
  Gets a single contact.

  Raises `Ecto.NoResultsError` if the Contact does not exist.

  ## Examples

      iex> get_contact!(123)
      %Contact{}

      iex> get_contact!(456)
      ** (Ecto.NoResultsError)

  """
  def get_contact!(id), do: Repo.get!(Contact, id)

  def get_contact(id), do: Repo.get(Contact, id)

  @doc """
  Gets a single contact.

  Returns nil if the Contact does not exist.

  Raises `Ecto.MultipleResultsError` if more than one entry found.

  ## Examples

      iex> get_contact_by(%{key: value})
      %Contact{}

      iex> get_contact_by(%{key: value})
      nil

      iex> get_contact_by(%{key: value})
      ** (Ecto.MultipleResultsError)

  """
  def get_contact_by(attrs), do: Repo.get_by(Contact, attrs)

  @doc """
  Creates a contact.

  ## Examples

      iex> create_contact(%{field: value})
      {:ok, %Contact{}}

      iex> create_contact(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_contact(attrs \\ %{}) do
    %Contact{}
    |> Contact.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a contact.

  ## Examples

      iex> update_contact(contact, %{field: new_value})
      {:ok, %Contact{}}

      iex> update_contact(contact, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_contact(%Contact{} = contact, attrs) do
    contact
    |> Contact.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a contact.

  ## Examples

      iex> delete_contact(contact)
      {:ok, %Contact{}}

      iex> delete_contact(contact)
      {:error, %Ecto.Changeset{}}

  """
  def delete_contact(%Contact{} = contact) do
    Repo.delete(contact)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking contact changes.

  ## Examples

      iex> change_contact(contact)
      %Ecto.Changeset{data: %Contact{}}

  """
  def change_contact(%Contact{} = contact, attrs \\ %{}) do
    Contact.changeset(contact, attrs)
  end

  @doc """
  Links contact with shareholdings.

  Checks if contact and shareholdings are within the same company.
  Checks if shareholdings do not already linked to another contact.

  Function is only successful if the number of updated rows equaled to the number of provided shareholding ids.
  """
  def link_contact_with_shareholding_ids(%Contact{} = contact, shareholding_ids, company_user_id) do
    utc_now = NaiveDateTime.utc_now(:second)

    Ecto.Multi.new()
    |> Ecto.Multi.update_all(
      :link,
      Shareholding
      |> where(company_profile_id: ^contact.company_profile_id)
      |> where([sh], sh.id in ^shareholding_ids),
      set: [
        contact_id: contact.id,
        contact_linked_at: utc_now,
        contact_linked_by_id: company_user_id
      ]
    )
    |> Ecto.Multi.update_all(
      :lead_status,
      Contact
      |> where(id: ^contact.id)
      |> where(company_profile_id: ^contact.company_profile_id)
      |> where([c], not is_nil(c.lead_identified_at) and is_nil(c.lead_converted_at)),
      set: [
        lead_converted_at: utc_now
      ]
    )
    |> Ecto.Multi.run(:check, fn _, %{link: {updated_count, _}} ->
      if updated_count == Enum.count(shareholding_ids) do
        {:ok, true}
      else
        {:error, false}
      end
    end)
    |> Repo.transaction()
  end

  @doc """
  Links a contact with a list of beneficial owner account IDs.

  ## Parameters
    - contact: The contact to link.
    - beneficial_owner_account_ids: The IDs of the beneficial owner accounts to link.

  ## Returns
    - {:ok, true} if the contact was successfully linked.
    - {:error, false} if the contact was not successfully linked.
  """
  def link_contact_with_beneficial_owner_account_ids(
        %Contact{company_profile_id: company_profile_id, id: contact_id},
        beneficial_owner_account_ids
      ) do
    utc_now = NaiveDateTime.utc_now(:second)
    beneficial_owner_account_ids = Enum.uniq(beneficial_owner_account_ids)

    Ecto.Multi.new()
    |> Ecto.Multi.update_all(
      :link_contact_to_beneficial_owner_accounts,
      Account
      |> where(company_profile_id: ^company_profile_id)
      |> where([boa], boa.id in ^beneficial_owner_account_ids),
      set: [
        contact_id: contact_id,
        updated_at: utc_now
      ]
    )
    |> Ecto.Multi.run(:check, fn _,
                                 %{
                                   link_contact_to_beneficial_owner_accounts: {updated_accounts_count, _}
                                 } ->
      if updated_accounts_count == Enum.count(beneficial_owner_account_ids) do
        {:ok, true}
      else
        {:error, false}
      end
    end)
    |> Repo.transaction()
  end

  @doc """
    Links a beneficial owner account to a new contact.

  ## Parameters
    - the beneficial owner account to link.
    - the new contact id

  ## Returns
    - {:ok, true} if the contact was successfully linked.
    - {:error, false} if the contact was not successfully linked.
  """

  # def relink_beneficial_owner_account_to_new_contact_id(%Account{id: account_id}, new_contact_id) do
  #   utc_now = NaiveDateTime.utc_now(:second)
  #
  #   Ecto.Multi.new()
  #   |> Ecto.Multi.run(:fetch_contact, fn _ ->
  #     contact = Contact |> where(id: ^new_contact_id) |> Repo.one()
  #     {:ok, contact}
  #   end)
  #   |> Ecto.Multi.run(
  #     :link_new_contact_to_beneficial_owner_account,
  #     fn _, %{fetch_contact: contact} ->
  #       Account
  #       |> where(company_profile_id: ^contact.company_profile_id)
  #       |> where(id: ^account_id)
  #       |> update(set: [contact_id: ^new_contact_id, updated_at: ^utc_now])
  #       |> Repo.update_all([])
  #
  #       {:ok, true}
  #     end
  #   )
  #   |> Repo.transaction()
  # end

  def wukong_get_contact(%{"company_profile_id" => company_profile_id} = rest, query) do
    query = where(query, [q], q.company_profile_id == ^company_profile_id)
    rest |> Map.drop(["company_profile_id"]) |> wukong_get_contact(query)
  end

  def wukong_get_contact(%{"keyword" => search} = rest, query) do
    query =
      where(
        query,
        [q],
        ilike(
          fragment("? || ' ' || ?", coalesce(q.first_name, ""), coalesce(q.last_name, "")),
          ^"%#{search}%"
        )
      )

    rest |> Map.drop(["keyword"]) |> wukong_get_contact(query)
  end

  def wukong_get_contact(%{"email" => email} = rest, query) do
    query = where(query, [q], ilike(q.email, ^"%#{email}%"))
    rest |> Map.drop(["email"]) |> wukong_get_contact(query)
  end

  def wukong_get_contact(_, query), do: Repo.all(query)

  # To export so cleaned names can be viewed
  def list_email_greeting_names(company_profile_id) do
    Contact
    |> where(company_profile_id: ^company_profile_id)
    |> Repo.all()
    |> Enum.map(fn contact ->
      %{
        id: contact.id,
        first_name: contact.first_name,
        email: contact.email,
        email_greeting_name: Gaia.Emails.NameCleaner.generate(contact)
      }
    end)
  end

  def send_email_greeting_names(company_profile_id) do
    names = list_email_greeting_names(company_profile_id)

    folder =
      Path.join([
        System.tmp_dir(),
        "email-greeting-names"
      ])

    File.mkdir_p!(folder)
    file_name = "#{folder}/email-greeting-names-profile_#{company_profile_id}.csv"

    file = File.open!(file_name, [:write, :utf8])

    names
    |> CSV.encode(
      headers: [
        id: "Contact ID",
        email: "Email",
        first_name: "First name",
        email_greeting_name: "Email Greeting Name"
      ]
    )
    |> Enum.each(&IO.write(file, &1))

    {:ok, stored_file_name} = EmailNames.store({file_name, company_profile_id})
    EmailNames.url({stored_file_name, company_profile_id})
  end

  @doc """
  Returns a list of contact ids for a specific company.

  Used in conjunction with `contacts_query/2` to get a list of contact ids.
  """
  def contact_ids(contacts_query) do
    contacts_query
    |> select([contact], contact.id)
    |> Repo.all()
  end

  @doc """
  Returns a contacts query for a specific company.

  Used name bindings for making it easier to compose query
  """
  def contacts_query(company_profile_id, options \\ %{}) do
    base_query =
      Contact
      |> from(as: :contact)
      |> where([contact: c], c.company_profile_id == ^company_profile_id)
      |> where([contact: c], not c.invalidated)

    Enum.reduce(options, base_query, fn
      {:orders, orders}, query_acc ->
        order_contacts_query(query_acc, orders)

      {:filters, filters}, query_acc ->
        filter_contacts_query(query_acc, filters, company_profile_id)
    end)
  end

  defp filters_contain?(key, filters) do
    Enum.any?(filters, fn filter -> Map.get(filter, :key) == key end)
  end

  # credo:disable-for-lines:85
  defp filter_contacts_query(query, filters, company_profile_id) do
    query_with_filters =
      Enum.reduce(filters, query, fn
        %{key: _, value: "none"}, query ->
          query

        %{key: _, value: "undefined"}, query ->
          query

        %{key: _, value: ""}, query ->
          query

        %{key: "exclude_ids", value: ""}, query ->
          query

        %{key: "exclude_ids", value: exclude_ids}, query ->
          where(query, [q], q.id not in ^String.split(exclude_ids, ","))

        %{key: "include_ids", value: ""}, query ->
          query

        %{key: "include_ids", value: include_ids}, query ->
          where(query, [q], q.id in ^String.split(include_ids, ","))

        %{key: "sources", value: sources}, query ->
          to_search = String.split(sources, ",")

          case to_search do
            [""] ->
              query

            _ ->
              atoms = Enum.map(to_search, &String.to_atom/1)
              where(query, [q], q.contact_source in ^atoms)
          end

        %{key: "start_date", value: ""}, query ->
          query

        %{key: "start_date", value: date}, query ->
          from(q in query,
            where: q.inserted_at >= ^NaiveDateTime.from_iso8601!(date)
          )

        %{key: "end_date", value: ""}, query ->
          query

        %{key: "end_date", value: date}, query ->
          from(q in query,
            where: q.inserted_at <= ^NaiveDateTime.from_iso8601!(date)
          )

        %{key: "is_name_available", value: "true"}, query ->
          from(q in query,
            where: not is_nil(q.first_name) or not is_nil(q.last_name)
          )

        %{key: "is_name_available", value: "false"}, query ->
          from(q in query,
            where: is_nil(q.first_name) and is_nil(q.last_name)
          )

        %{key: "is_number_avaliable", value: "true"}, query ->
          from(q in query,
            where: not is_nil(q.phone_number)
          )

        %{key: "has_email", value: "true"}, query ->
          where(query, [q], not is_nil(q.email))

        %{key: "has_email", value: "false"}, query ->
          where(query, [q], is_nil(q.email))

        %{key: "has_email", value: "suppressed"}, query ->
          join(query, :inner, [q], cs in ContactSuppression, on: cs.contact_id == q.id)

        %{key: "has_email", value: "invalid"}, query ->
          where(query, [q], q.email_validity_result == ^:invalid)

        %{key: "has_email", value: "undeliverable"}, query ->
          # suppressed, invalid, and null email contact_ids
          suppressed_ids =
            ContactSuppression
            |> where([cs], cs.company_profile_id == ^company_profile_id)
            |> select([cs], cs.contact_id)

          invalid_ids =
            Contact
            |> where(
              [c],
              c.company_profile_id == ^company_profile_id and c.email_validity_result == ^:invalid
            )
            |> select([c], c.id)

          null_email_ids =
            Contact
            |> where([c], c.company_profile_id == ^company_profile_id and is_nil(c.email))
            |> select([c], c.id)

          where(
            query,
            [q],
            q.id in subquery(null_email_ids) or q.id in subquery(suppressed_ids) or
              q.id in subquery(invalid_ids)
          )

        %{key: "is_number_avaliable", value: "false"}, query ->
          from(q in query,
            where: is_nil(q.phone_number)
          )

        %{key: "hnw_status", value: "not-indicated"}, query ->
          where(query, [q], is_nil(q.hnw_status) or is_nil(q.hnw_identified_at))

        %{key: "hnw_status", value: hnw_type}, query ->
          where(query, [q], q.hnw_status == ^hnw_type and not is_nil(q.hnw_identified_at))

        %{key: "has_shareholding", value: "linked-only"}, query ->
          where(
            query,
            [contact: c],
            c.id in subquery(linked_shareholdings_count_query(company_profile_id))
          )

        %{key: "has_shareholding", value: "unlinked-only"}, query ->
          where(
            query,
            [contact: c],
            c.id not in subquery(linked_shareholdings_count_query(company_profile_id))
          )

        %{key: "shareholder_status", value: "nominated-shareholder"}, query ->
          investor_self_nominated_shareholder_query =
            Gaia.Investors.User
            |> where(company_profile_id: ^company_profile_id)
            |> where([iu], not is_nil(iu.contact_id))
            |> where([iu], iu.is_self_nominated_shareholder)
            |> select([iu], iu.contact_id)

          query
          |> where(
            [contact: c],
            c.is_nominated_shareholder or
              c.id in subquery(investor_self_nominated_shareholder_query)
          )
          |> where(
            [contact: c],
            c.id not in subquery(linked_shareholdings_count_query(company_profile_id))
          )

        %{key: "shareholder_status", value: "nominated-shareholder-with-holding-info"}, query ->
          contact_ids_query =
            Contact
            |> join(:left, [q], assoc(q, :investor))
            |> join(:left, [q, iu], assoc(iu, :shareholder_information))
            |> join(:left, [q, iu], assoc(iu, :shareholder_information_uk))
            |> where([q, iu], q.is_nominated_shareholder or iu.is_self_nominated_shareholder)
            |> where([q, iu, si, siu], not is_nil(si.id) or not is_nil(siu.id))
            |> where(
              [q],
              q.id not in subquery(linked_shareholdings_count_query(company_profile_id))
            )
            |> select([q], q.id)

          where(query, [q], q.id in subquery(contact_ids_query))

        %{key: "shareholder_status", value: "nominated-shareholder-without-holding-info"}, query ->
          contact_ids_query =
            Contact
            |> join(:left, [q], assoc(q, :investor))
            |> join(:left, [q, iu], assoc(iu, :shareholder_information))
            |> join(:left, [q, iu], assoc(iu, :shareholder_information_uk))
            |> where([q, iu], q.is_nominated_shareholder or iu.is_self_nominated_shareholder)
            |> where([q, iu, si, siu], is_nil(si.id) and is_nil(siu.id))
            |> where(
              [q],
              q.id not in subquery(linked_shareholdings_count_query(company_profile_id))
            )
            |> select([q], q.id)

          where(query, [q], q.id in subquery(contact_ids_query))

        %{key: "shareholder_status", value: "shareholder"}, query ->
          where(
            query,
            [contact: c],
            c.id in subquery(linked_current_shareholdings_count_query(company_profile_id))
          )

        %{key: "shareholder_status", value: "past-shareholder"}, query ->
          where(
            query,
            [contact: c],
            c.id in subquery(linked_past_shareholdings_count_query(company_profile_id))
          )

        %{key: "shareholder_status", value: "not-shareholder"}, query ->
          contacts_id_query =
            Contact
            |> join(:left, [q], iu in assoc(q, :investor))
            |> where([q], not q.is_nominated_shareholder)
            |> where(
              [q, iu],
              is_nil(iu.id) or (not is_nil(iu.id) and not iu.is_self_nominated_shareholder)
            )
            |> where(
              [q],
              q.id not in subquery(linked_shareholdings_count_query(company_profile_id))
            )
            |> select([q], q.id)

          where(query, [contact: c], c.id in subquery(contacts_id_query))

        %{key: "subscription_status", value: "global-unsubscribed"}, query ->
          build_filter_query(
            %{key: "email_subscribes", value: "nothing"},
            query,
            company_profile_id
          )

        %{key: "subscription_status", value: "suppressed"}, query ->
          join(query, :inner, [q], cs in ContactSuppression, on: cs.contact_id == q.id)

        %{key: "subscription_status", value: ""}, query ->
          query

        %{key: "subscription_status", value: value}, query ->
          build_filter_query(%{key: "email_subscribes", value: value}, query, company_profile_id)

        %{key: "newholder_status", value: "30"}, query ->
          query
          |> join(:inner, [q], sh in Shareholding, on: sh.contact_id == q.id)
          |> where(
            [q, sh],
            q.id in subquery(linked_new_shareholdings_count_query(company_profile_id))
          )

        %{key: "newholder_status", value: "60"}, query ->
          query
          |> join(:inner, [q], sh in Shareholding, on: sh.contact_id == q.id)
          |> where(
            [q, sh],
            q.id in subquery(linked_new_shareholdings_count_query(company_profile_id, 60))
          )

        %{key: "newholder_status", value: "90"}, query ->
          query
          |> join(:inner, [q], sh in Shareholding, on: sh.contact_id == q.id)
          |> where(
            [q, sh],
            q.id in subquery(linked_new_shareholdings_count_query(company_profile_id, 90))
          )

        %{key: "lead_status", value: "not-investor-lead"}, query ->
          query
          |> join(:inner, [q], sh in Shareholding, on: sh.contact_id == q.id)
          |> where(
            [q, sh],
            q.id in subquery(linked_shareholdings_count_query(company_profile_id))
          )

        %{key: "lead_status", value: "investor-lead"}, query ->
          query
          |> join(:left, [q], sh in Shareholding, on: sh.contact_id == q.id)
          |> where(
            [q, sh],
            q.id not in subquery(linked_shareholdings_count_query(company_profile_id))
          )

        %{key: "has_investor_hub_user", value: "linked-only"}, query ->
          where(
            query,
            [q],
            q.id in subquery(linked_investors_users_count_query(company_profile_id))
          )

        %{key: "has_investor_hub_user", value: "unlinked-only"}, query ->
          where(
            query,
            [q],
            q.id not in subquery(linked_investors_users_count_query(company_profile_id))
          )

        %{key: "tags", value: tags}, query ->
          contacts_query_apply_tags(query, tags)

        %{key: "static_list_ids", value: ids}, query ->
          static_list_query_apply_ids(query, ids)

        %{key: "hub_sign_ups_days_ago", value: "30"}, query ->
          query
          |> join(:left, [q], iu in User, on: iu.contact_id == q.id)
          |> where(
            [q, iu],
            q.id in subquery(new_sign_up_count_query(company_profile_id))
          )

        %{key: "search", value: search}, query ->
          where(
            query,
            [q],
            ilike(
              fragment("? || ' ' || ?", coalesce(q.first_name, ""), coalesce(q.last_name, "")),
              ^"%#{search}%"
            ) or
              ilike(coalesce(q.email, ""), ^"%#{search}%")
          )

        %{key: "search_with_investor_users", value: search}, query ->
          query
          |> join(:left, [q], iu in User, on: iu.contact_id == q.id)
          |> where(
            [q, iu],
            ilike(
              fragment("? || ' ' || ?", coalesce(q.first_name, ""), coalesce(q.last_name, "")),
              ^"%#{search}%"
            ) or
              ilike(coalesce(q.email, ""), ^"%#{search}%") or
              ilike(coalesce(iu.username, ""), ^"%#{search}%")
          )

        %{key: "search_with_sunrice_grower_number", value: search}, query ->
          query
          |> join(:left, [q], iu in User, on: iu.contact_id == q.id)
          |> where(
            [q, iu],
            ilike(
              fragment("? || ' ' || ?", coalesce(q.first_name, ""), coalesce(q.last_name, "")),
              ^"%#{search}%"
            ) or
              ilike(coalesce(q.email, ""), ^"%#{search}%") or
              ilike(coalesce(iu.username, ""), ^"%#{search}%") or
              ilike(q.sunrice_grower_number, ^"%#{search}%")
          )

        # the min and max months that a contact has held shares for (only current shareholdings)
        # TODO: we'll need to change the hardcoded timezone when new regions get registry data
        %{key: "min_months", value: min_months_held}, query ->
          min_months_held =
            case Integer.parse(min_months_held) do
              {int, ""} -> int
              _ -> min_months_held |> String.to_float() |> Float.round(2)
            end

          query
          |> join(:left, [q], sh in Shareholding,
            as: :min_shareholding,
            on: sh.contact_id == q.id
          )
          |> where(
            [q],
            q.id in subquery(linked_current_shareholdings_count_query(company_profile_id)) and
              fragment(
                "? + interval '1 months' * ? <= DATE(now() AT TIME ZONE 'Australia/Sydney')",
                as(:min_shareholding).current_holding_start_date,
                ^min_months_held
              )
          )

        %{key: "max_months", value: max_months_held}, query ->
          max_months_held =
            case Integer.parse(max_months_held) do
              {int, ""} -> int
              _ -> max_months_held |> String.to_float() |> Float.round(2)
            end

          query
          |> join(:left, [q], sh in Shareholding,
            as: :max_shareholding,
            on: sh.contact_id == q.id
          )
          |> where(
            [q],
            q.id in subquery(linked_current_shareholdings_count_query(company_profile_id)) and
              fragment(
                "? + interval '1 months' * ? >= DATE(now() AT TIME ZONE 'Australia/Sydney')",
                as(:max_shareholding).current_holding_start_date,
                ^max_months_held
              )
          )

        %{key: "traits", value: "top_20"}, query ->
          where(
            query,
            [q],
            q.id in subquery(top_x_contacts_total_shareholdings_to_contact_query(20, company_profile_id))
          )

        %{key: "traits", value: "top_50"}, query ->
          where(
            query,
            [q],
            q.id in subquery(top_x_contacts_total_shareholdings_to_contact_query(50, company_profile_id))
          )

        %{key: "broker_short_names", value: broker_short_names}, query ->
          where(
            query,
            [q],
            q.id in subquery(filter_shareholdings_by_broker_short_name(broker_short_names, company_profile_id))
          )

        %{key: "brokers_disclosed", value: brokers_disclosed}, query ->
          where(
            query,
            [q],
            q.id in subquery(
              Gaia.Investors.filter_shareholder_informations_uk_by_broker(
                brokers_disclosed,
                company_profile_id
              )
            )
          )

        %{key: "has_beneficial_owner_account", value: "true"}, query ->
          where(
            query,
            [q],
            q.id in subquery(contacts_with_beneficial_owner_accounts_query(company_profile_id))
          )

        %{key: "has_beneficial_owner_account", value: "false"}, query ->
          where(
            query,
            [q],
            q.id not in subquery(contacts_with_beneficial_owner_accounts_query(company_profile_id))
          )

        %{key: "beneficial_owner_shares_count", value: shares_count}, query ->
          case String.split(shares_count, ",") do
            [min_shares] ->
              min_shares_int = String.to_integer(min_shares)

              where(
                query,
                [q],
                q.id in subquery(contacts_with_beneficial_owner_shares_query(company_profile_id, min_shares_int, nil))
              )

            [min_shares, max_shares] ->
              min_shares_int = String.to_integer(min_shares)
              max_shares_int = String.to_integer(max_shares)

              where(
                query,
                [q],
                q.id in subquery(
                  contacts_with_beneficial_owner_shares_query(company_profile_id, min_shares_int, max_shares_int)
                )
              )

            _ ->
              query
          end

        filter, query ->
          build_filter_query(filter, query, company_profile_id)
      end)

    # Don't use distinct if the below filters are applied
    # Will break some of the campaigns funtionality as they are using their own distinct
    if filters_contain?("unsubscribed", filters) ||
         filters_contain?("campaign_email_audience_list", filters) do
      query_with_filters
    else
      distinct(query_with_filters, true)
    end
  end

  defp filter_shareholdings_by_broker_short_name(broker_short_names, company_profile_id) do
    short_names = String.split(broker_short_names, ",")

    broker_pids = Gaia.Registers.broker_short_names_to_pids(short_names)

    Shareholding
    |> where([sh], sh.company_profile_id == ^company_profile_id)
    |> where([sh], not is_nil(sh.broker_pid) and sh.broker_pid != "")
    |> where([sh], not is_nil(sh.contact_id))
    |> where([sh], sh.broker_pid in ^broker_pids)
    |> select([sh], sh.contact_id)
  end

  def top_x_contacts_total_shareholdings_to_contact_query(top_x, company_profile_id) do
    from(q in Contact,
      join: sh in Shareholding,
      on: sh.contact_id == q.id,
      where: q.company_profile_id == ^company_profile_id,
      group_by: q.id,
      order_by: [desc: sum(sh.share_count)],
      limit: ^top_x,
      select: q.id
    )
  end

  def linked_shareholdings_count_query(company_profile_id) do
    from(sh in Shareholding,
      where: sh.company_profile_id == ^company_profile_id and not is_nil(sh.contact_id),
      group_by: sh.contact_id,
      having: count(sh.contact_id) > 0,
      select: sh.contact_id
    )
  end

  def linked_current_shareholdings_count_query(company_profile_id) do
    from(sh in Shareholding,
      where: sh.company_profile_id == ^company_profile_id and not is_nil(sh.contact_id),
      group_by: sh.contact_id,
      having: count(sh.contact_id) > 0 and sum(sh.share_count) > 0,
      select: sh.contact_id
    )
  end

  def linked_new_shareholdings_count_query(company_profile_id, new_in_last_x_days \\ 30) do
    now = Helper.ExDay.now_date()
    start_date = Timex.shift(now, days: -new_in_last_x_days)

    from(c in Contact,
      join: sh in assoc(c, :shareholdings),
      where: c.company_profile_id == ^company_profile_id,
      group_by: sh.contact_id,
      having:
        count(sh.contact_id) > 0 and sum(sh.share_count) > 0 and
          min(sh.initial_purchase_date) >= ^start_date,
      select: sh.contact_id
    )
  end

  def linked_past_shareholdings_count_query(company_profile_id) do
    from(sh in Shareholding,
      where: sh.company_profile_id == ^company_profile_id and not is_nil(sh.contact_id),
      group_by: sh.contact_id,
      having: count(sh.contact_id) > 0 and sum(sh.share_count) == 0,
      select: sh.contact_id
    )
  end

  def contacts_with_beneficial_owner_accounts_query(company_profile_id) do
    from(boa in Account,
      where: boa.company_profile_id == ^company_profile_id and not is_nil(boa.contact_id),
      group_by: boa.contact_id,
      having: count(boa.contact_id) > 0,
      select: boa.contact_id
    )
  end

  def contacts_with_beneficial_owner_shares_query(company_profile_id, min_shares, max_shares \\ nil) do
    latest_holdings_query =
      from(h in Gaia.BeneficialOwners.Holding,
        join: r in assoc(h, :report),
        join: boa in assoc(h, :beneficial_owner_account),
        where: h.company_profile_id == ^company_profile_id and not is_nil(boa.contact_id),
        order_by: [desc: r.report_date],
        distinct: h.beneficial_owner_account_id,
        select: %{
          contact_id: boa.contact_id,
          shares: h.shares
        }
      )

    base_query =
      from(lh in subquery(latest_holdings_query),
        group_by: lh.contact_id,
        having: sum(lh.shares) >= ^min_shares,
        select: lh.contact_id
      )

    if max_shares do
      from(lh in subquery(latest_holdings_query),
        group_by: lh.contact_id,
        having: sum(lh.shares) >= ^min_shares and sum(lh.shares) <= ^max_shares,
        select: lh.contact_id
      )
    else
      base_query
    end
  end

  def invalidate_contacts(contacts_to_invalidate, company_profile_id) do
    updated_at = NaiveDateTime.utc_now(:second)

    Contact
    |> where([c], c.company_profile_id == ^company_profile_id)
    |> where([c], c.id in ^contacts_to_invalidate)
    |> update(set: [invalidated: true, updated_at: ^updated_at])
    |> Repo.update_all([])
  end

  def contacts_by_ids(ids, company_profile_id) do
    Contact
    |> where([c], c.company_profile_id == ^company_profile_id)
    |> where([c], c.id in ^ids)
    |> Repo.all()
  end

  defp linked_investors_users_count_query(company_profile_id) do
    from(iu in User,
      where: iu.company_profile_id == ^company_profile_id and not is_nil(iu.contact_id),
      group_by: iu.contact_id,
      having: count(iu.contact_id) > 0,
      select: iu.contact_id
    )
  end

  def new_sign_up_count_query(company_profile_id, new_in_last_x_days \\ 30) do
    # The time should be 00:00:00 to be consistent with AthenaWeb.Resolvers.Analysis.InvestorHubAnalytics.resolve
    start_date =
      :second
      |> NaiveDateTime.utc_now()
      |> Timex.shift(days: -new_in_last_x_days)
      |> Timex.beginning_of_day()

    User
    |> where(company_profile_id: ^company_profile_id)
    |> where([iu], not is_nil(iu.contact_id) and iu.inserted_at >= ^start_date)
    |> select([iu], iu.contact_id)
  end

  defp contacts_query_apply_tags(query, ""), do: query

  defp contacts_query_apply_tags(query, tags) do
    search_tags = String.split(tags, ",")

    query
    |> join(:left, [contact], t in assoc(contact, :tags),
      as: :tag,
      on: t.company_profile_id == contact.company_profile_id
    )
    |> where([tag: tag], tag.name in ^search_tags)
  end

  defp static_list_query_apply_ids(query, ""), do: query

  defp static_list_query_apply_ids(query, ids) do
    where(
      query,
      [q],
      q.id in subquery(
        StaticListMember
        |> where([slm], slm.static_list_id in ^String.split(ids, ","))
        |> select([slm], slm.contact_id)
      )
    )
  end

  # We should exclude contacts who have unsubscribed to the selected scope
  defp build_filter_query(%{key: "subscription_scope", value: scope}, query, company_profile_id) do
    # Prepare the contact ids that we need to filter out
    global_unsub_query =
      ContactGlobalUnsubscribe
      |> where(company_profile_id: ^company_profile_id)
      |> select([cgu], cgu.contact_id)

    suppression_query =
      ContactSuppression
      |> where(company_profile_id: ^company_profile_id)
      |> select([cs], cs.contact_id)

    contact_ids_to_exclude =
      ContactUnsubscribe
      |> where(company_profile_id: ^company_profile_id)
      |> where(scope: ^scope)
      |> select([cu], cu.contact_id)
      |> subquery()
      |> union(^global_unsub_query)
      |> subquery()
      |> select([q], q.contact_id)
      |> union(^suppression_query)
      |> subquery()
      |> where([s], not is_nil(s.contact_id))
      |> select([s], s.contact_id)
      |> Repo.all()

    where(query, [contact: contact], contact.id not in ^contact_ids_to_exclude)
  end

  # Example filter: %{key: "campaign_email_audience_list", value: "general,ALL,ALL_SHAREHOLDERS,NEW_SHAREHOLDERS,HUB,IMPORTED"}
  # The first value is the unsubscriber scope
  # Allows duplicate contacts in returned result, in order to resolve number of duplicates in frontend
  # Matt 26/2/24: In future this will be deprecated.
  # This function is used for creating groups of contacts to send an email to (eg for an announcement/update).
  # But we ideally want the contact search to be the same as when creating a campaign.
  # When creating a campaign you start with all contacts and then drill down with filters. Unfortunately it will then save the results in the DB - which is bad for a scheduled email as it will miss any new contacts that enter the system between now and the email sending.
  # So we need a unified version where the filters are saved, and the query is run right before sending the email to fetch the contacts.
  defp build_filter_query(%{key: "campaign_email_audience_list", value: value}, query, company_profile_id) do
    input = String.split(value, ",")
    unsubscriber_scope = Enum.at(input, 0)
    audiences = Enum.slice(input, 1..-1//1)

    # Prepare the contact ids that we need to filter out
    global_unsub_query =
      ContactGlobalUnsubscribe
      |> where(company_profile_id: ^company_profile_id)
      |> select([cgu], cgu.contact_id)

    suppression_query =
      ContactSuppression
      |> where(company_profile_id: ^company_profile_id)
      |> where([cc], not is_nil(cc.contact_id))
      |> select([cs], cs.contact_id)

    contact_ids_to_exclude =
      ContactUnsubscribe
      |> where(company_profile_id: ^company_profile_id)
      |> where(scope: ^unsubscriber_scope)
      |> select([cu], cu.contact_id)
      |> subquery()
      |> union(^global_unsub_query)
      |> subquery()
      |> select([q], q.contact_id)
      |> union(^suppression_query)
      |> subquery()
      |> select([s], s.contact_id)
      |> Repo.all()

    base_query =
      where(
        query,
        [contact: contact],
        contact.id not in ^contact_ids_to_exclude
      )

    campaign_email_audience_list_query(
      base_query,
      audiences,
      company_profile_id,
      Enum.member?(audiences, "ALL")
    )
  end

  # Show contact who has not unsubscribed at all
  defp build_filter_query(%{key: "email_subscribes", value: "all"}, query, _) do
    query
    |> join(
      :left,
      [contact: contact],
      assoc(contact, :comms_unsubscribes),
      as: :contact_unsubscribe
    )
    |> join(
      :left,
      [contact: contact],
      assoc(contact, :global_unsubscribe),
      as: :contact_global_unsubscribe
    )
    |> join(
      :left,
      [contact: contact],
      assoc(contact, :suppression),
      as: :contact_suppression
    )
    |> where([contact: contact], not contact.invalidated)
    |> where([contact_unsubscribe: cu], is_nil(cu.id))
    |> where([contact_global_unsubscribe: cgu], is_nil(cgu.id))
    |> where([contact_suppression: cs], is_nil(cs.id))
  end

  # Show contact who has globally unsubscribed or have unsubscribed to all 5 scope
  defp build_filter_query(%{key: "email_subscribes", value: "nothing"}, query, company_profile_id) do
    unsubscribed_all_query =
      ContactUnsubscribe
      |> where(company_profile_id: ^company_profile_id)
      |> group_by([cu], cu.contact_id)
      |> having([cu], count(cu.contact_id) == 5)
      |> select([cu], %{contact_id: cu.contact_id})

    query
    |> join(
      :left,
      [contact: contact],
      cu in subquery(unsubscribed_all_query),
      on: cu.contact_id == contact.id,
      as: :contact_unsubscribe
    )
    |> join(
      :left,
      [contact: contact],
      assoc(contact, :global_unsubscribe),
      as: :contact_global_unsubscribe
    )
    |> where([contact: contact], not contact.invalidated)
    |> where(
      [contact_unsubscribe: cu, contact_global_unsubscribe: cgu],
      not is_nil(cu.contact_id) or not is_nil(cgu.id)
    )
  end

  # Show contact who has subscribed to all requested scope
  # value could be "scope_one,scope_two"
  defp build_filter_query(%{key: "email_subscribes", value: value}, query, company_profile_id) do
    scopes = String.split(value, ",")

    # contact_id who has unsubscribed from the scope(s)
    unsubscriber_query =
      ContactUnsubscribe
      |> where(company_profile_id: ^company_profile_id)
      |> where([cu], cu.scope in ^scopes)
      |> select([cu], cu.contact_id)

    # contact_id who has globally unsubscribed
    global_unsubscriber_query =
      ContactGlobalUnsubscribe
      |> where(company_profile_id: ^company_profile_id)
      |> select([cgu], cgu.contact_id)

    # contact_id who has been suppressed
    suppressed_query =
      ContactSuppression
      |> where(company_profile_id: ^company_profile_id)
      |> where([cs], not is_nil(cs.contact_id))
      |> select([cs], cs.contact_id)

    # Find contact of a company that are nor subscribed from the scope nor globally unsubscribed nor suppressed
    query
    |> where([contact: contact], not contact.invalidated)
    |> where(
      [contact: contact],
      contact.id not in subquery(
        unsubscriber_query
        |> union(^global_unsubscriber_query)
        |> union(^suppressed_query)
      )
    )
  end

  defp build_filter_query(%{key: "source", value: "shareholder"}, query, company_profile_id) do
    sh_query =
      Gaia.Registers.Shareholding
      |> where(company_profile_id: ^company_profile_id)
      |> where([sh], not is_nil(sh.contact_id))
      |> select([sh], %{contact_id: sh.contact_id})
      |> distinct(true)

    query
    |> where([contact: contact], not contact.invalidated)
    |> where([contact: contact], contact.id in subquery(sh_query))
  end

  defp build_filter_query(%{key: "source", value: "hub"}, query, _) do
    from(q in query,
      join: u in Gaia.Investors.User,
      on: u.contact_id == q.id,
      where: not q.invalidated
    )
  end

  defp build_filter_query(%{key: "source", value: "imported"}, query, _) do
    where(query, [contact: c], not is_nil(c.imported_at))
  end

  # Show contact who has unsubscribed to anything
  defp build_filter_query(%{key: "unsubscribed", value: "all"}, query, _) do
    query
    |> join(
      :left,
      [contact: contact],
      cu in assoc(contact, :comms_unsubscribes),
      as: :contact_unsubscribe
    )
    |> join(
      :left,
      [contact: contact],
      assoc(contact, :global_unsubscribe),
      as: :contact_global_unsubscribe
    )
    |> where([contact: contact], not contact.invalidated)
    |> where(
      [contact_unsubscribe: cu, contact_global_unsubscribe: cgu],
      not is_nil(cu.id) or not is_nil(cgu.id)
    )
    |> distinct([contact: contact], contact.id)
  end

  # Show contact who is also a shareholding and has unsubscribed to anything
  defp build_filter_query(%{key: "unsubscribed", value: "shareholder"}, query, company_profile_id) do
    base_query = join(query, :inner, [contact: contact], assoc(contact, :shareholdings))

    build_filter_query(%{key: "unsubscribed", value: "all"}, base_query, company_profile_id)
  end

  # Show contact who is also a investor and has unsubscribed to anything
  defp build_filter_query(%{key: "unsubscribed", value: "hub"}, query, company_profile_id) do
    base_query = join(query, :inner, [contact: contact], assoc(contact, :investor))

    build_filter_query(%{key: "unsubscribed", value: "all"}, base_query, company_profile_id)
  end

  # Show imported contact who has unsubscribed to anything
  defp build_filter_query(%{key: "unsubscribed", value: "imported"}, query, company_profile_id) do
    %{key: "unsubscribed", value: "all"}
    |> build_filter_query(query, company_profile_id)
    |> where([contact: contact], not is_nil(contact.imported_at))
  end

  defp build_filter_query(%{key: "location", value: location}, query, _) do
    locations = location |> String.split(",") |> Enum.map(&String.upcase(&1))
    states = Enum.filter(locations, &(!Enum.member?(["NZ", "OTHER"], &1)))
    countries = Enum.filter(locations, &Enum.member?(["NZ", "OTHER"], &1))

    case locations do
      [""] ->
        query

      _ ->
        where(
          query,
          ^dynamic(
            [q],
            ^filter_contacts_where_states(states) or ^filter_contacts_where_countries(countries)
          )
        )
    end
  end

  # Expected input is `{type},{start_date},{end_date}` or `{type},{last_n_days}`
  # i.e. `new,2024-05-01,2024-05-07` or `returning,30
  defp build_filter_query(%{key: "trading_activity", value: input}, query, company_profile_id) do
    # Anonymous helper functions - START

    # start_date, end_date, latest_date
    get_end_date_to_use = fn
      %Date{} = start_date, %Date{} = end_date, nil ->
        Enum.max([start_date, end_date], Date)

      %Date{} = start_date, %Date{} = end_date, %Date{} = latest_date ->
        # Find the minimum date between end date requested and latest daily holding date.
        # Then find the maximum date between start date requested and the result of the previous step.
        # This is to ensure that the observed start date is not after the observed end date.
        # Preventing start_date_to_use ~D[2024-06-24] and end_date_to_use ~D[2024-05-01]
        # and causes the logic to treat everyone as buyers (edge case when company last import is 2024-05-01)
        Enum.max([start_date, Enum.min([end_date, latest_date], Date)], Date)
    end

    shareholding_balance_query = fn profile_id, %Date{} = date ->
      initial_purchase_date_query =
        Gaia.Registers.Shareholding
        |> where(company_profile_id: ^profile_id)
        |> where([sh], not is_nil(sh.contact_id))
        |> group_by([sh], sh.contact_id)
        |> select([sh], %{
          contact_id: sh.contact_id,
          initial_purchase_date: min(sh.initial_purchase_date)
        })

      Gaia.Registers.DailyHolding
      |> join(:inner, [dh], assoc(dh, :shareholding))
      |> join(:inner, [dh, sh], ipd in subquery(initial_purchase_date_query), on: ipd.contact_id == sh.contact_id)
      |> where([dh], dh.company_profile_id == ^profile_id)
      |> where([dh], dh.date == ^date)
      |> where([_dh, sh], not is_nil(sh.contact_id))
      |> group_by([_dh, sh, ipd], [sh.contact_id, ipd.initial_purchase_date])
      |> select([dh, sh, ipd], %{
        contact_id: sh.contact_id,
        balance: sum(dh.balance),
        initial_purchase_date: ipd.initial_purchase_date
      })
    end

    # Anonymous helper functions - END

    [type, %Date{} = start_date, %Date{} = end_date] =
      input
      |> String.split(",")
      |> case do
        [string_1, string_2] ->
          now_date = Helper.ExDay.now_date()
          previous_date = Date.add(now_date, -String.to_integer(string_2))

          [String.downcase(String.trim(string_1)), previous_date, now_date]

        [string_1, string_2, string_3] ->
          [
            String.downcase(String.trim(string_1)),
            Date.from_iso8601!(string_2),
            Date.from_iso8601!(string_3)
          ]
      end

    # Throw an error if start_date is after end_date
    # We allow if start_date is the same as end_date (movement within 1 day)
    false = Timex.after?(start_date, end_date)

    # Get the latest date of the daily holding data
    # The maximum possible end date should be the latest date of the daily holding data
    latest_date =
      Gaia.Registers.get_latest_daily_holding_date_by_company_profile(company_profile_id)

    # The start_date need to shift backwards by 1 day because we want to measure from the beginning of the day
    # Registry data is updated daily so the beginning of the day equals the end of the previous day
    start_date_to_use = Date.add(start_date, -1)
    end_date_to_use = get_end_date_to_use.(start_date, end_date, latest_date)

    shareholding_at_start_query =
      shareholding_balance_query.(company_profile_id, start_date_to_use)

    shareholding_at_end_query = shareholding_balance_query.(company_profile_id, end_date_to_use)

    base_query =
      shareholding_at_start_query
      |> subquery()
      |> join(:full, [s], e in subquery(shareholding_at_end_query), on: e.contact_id == s.contact_id)
      |> select([s, e], %{
        contact_id: coalesce(s.contact_id, e.contact_id),
        start_balance: coalesce(s.balance, 0),
        end_balance: coalesce(e.balance, 0),
        initial_purchase_date: coalesce(s.initial_purchase_date, e.initial_purchase_date)
      })
      |> subquery()

    contact_ids_query =
      type
      |> case do
        "new" ->
          base_query
          |> where([bq], bq.start_balance == 0)
          |> where([bq], bq.end_balance > 0)
          |> where([bq], bq.initial_purchase_date > ^start_date_to_use)

        "returning" ->
          base_query
          |> where([bq], bq.start_balance == 0)
          |> where([bq], bq.end_balance > 0)
          |> where([bq], bq.initial_purchase_date <= ^start_date_to_use)

        "churned" ->
          base_query
          |> where([bq], bq.start_balance > 0)
          |> where([bq], bq.end_balance == 0)

        "upgrader" ->
          base_query
          |> where([bq], bq.start_balance != 0)
          |> where([bq], bq.end_balance != 0)
          |> where([bq], bq.start_balance < bq.end_balance)

        "downgrader" ->
          base_query
          |> where([bq], bq.start_balance != 0)
          |> where([bq], bq.end_balance != 0)
          |> where([bq], bq.start_balance > bq.end_balance)
      end
      |> select([bq], bq.contact_id)

    where(query, [q], q.id in subquery(contact_ids_query))
  end

  defp build_filter_query(%{key: "min_share_count", value: input}, query, company_profile_id) do
    min_share_count = String.to_integer(input)

    contact_ids_query =
      Gaia.Registers.Shareholding
      |> where(company_profile_id: ^company_profile_id)
      |> where([sh], not is_nil(sh.contact_id))
      |> group_by([sh], sh.contact_id)
      |> having([sh], sum(sh.share_count) >= ^min_share_count)
      |> select([sh], sh.contact_id)

    where(query, [q], q.id in subquery(contact_ids_query))
  end

  defp build_filter_query(%{key: "max_share_count", value: input}, query, company_profile_id) do
    max_share_count = String.to_integer(input)

    contact_ids_query =
      Gaia.Registers.Shareholding
      |> where(company_profile_id: ^company_profile_id)
      |> where([sh], not is_nil(sh.contact_id))
      |> group_by([sh], sh.contact_id)
      |> having([sh], sum(sh.share_count) <= ^max_share_count)
      |> select([sh], sh.contact_id)

    where(query, [q], q.id in subquery(contact_ids_query))
  end

  # For UK - filter by shares the investor user has claimed to have
  defp build_filter_query(%{key: "min_share_count_disclosed", value: input}, query, company_profile_id) do
    min_share_count = String.to_integer(input)

    contact_ids_query =
      Contact
      |> where(company_profile_id: ^company_profile_id)
      |> join(:left, [q], assoc(q, :investor))
      |> join(:left, [q, iu], assoc(iu, :shareholder_informations_uk))
      |> having([q, iu, siu], sum(siu.shares_owned) >= ^min_share_count)
      |> group_by([q], q.id)
      |> select([q], q.id)

    where(query, [q], q.id in subquery(contact_ids_query))
  end

  defp build_filter_query(%{key: "max_share_count_disclosed", value: input}, query, company_profile_id) do
    max_share_count = String.to_integer(input)

    contact_ids_query =
      Contact
      |> where(company_profile_id: ^company_profile_id)
      |> join(:left, [q], assoc(q, :investor))
      |> join(:left, [q, iu], assoc(iu, :shareholder_informations_uk))
      |> having([q, iu, siu], sum(siu.shares_owned) <= ^max_share_count)
      |> group_by([q], q.id)
      |> select([q], q.id)

    where(query, [q], q.id in subquery(contact_ids_query))
  end

  defp build_filter_query(_filter, query, _company_profile_id), do: query

  defp campaign_email_audience_list_query(query, _audiences, _company_profile_id, true) do
    query
  end

  defp campaign_email_audience_list_query(base_query, audiences, company_profile_id, false) do
    all_shareholders_query =
      if Enum.member?(audiences, "ALL_SHAREHOLDERS") do
        from(q in base_query,
          left_join: shareholder in Gaia.Registers.Shareholding,
          on: shareholder.contact_id == q.id,
          where: not is_nil(shareholder.id) and shareholder.share_count > 0
        )
      else
        from(q in base_query, where: q.id == -1)
      end

    hub_users_query =
      if Enum.member?(audiences, "HUB") do
        from(q in base_query,
          left_join: hub_user in Gaia.Investors.User,
          on: hub_user.contact_id == q.id,
          where: not is_nil(hub_user.id)
        )
      else
        from(q in base_query, where: q.id == -1)
      end

    imported_contacts_query =
      if Enum.member?(audiences, "IMPORTED") do
        from(q in base_query, where: not is_nil(q.imported_at))
      else
        from(q in base_query, where: q.id == -1)
      end

    end_date = Helper.ExDay.now_date()
    start_date = Timex.shift(end_date, days: -90)

    shareholdings_activity_query =
      Gaia.Dashboard.shareholder_trading_activity_query(
        company_profile_id,
        start_date,
        end_date,
        "new",
        nil
      )

    new_shareholders_query =
      if not Enum.member?(audiences, "ALL_SHAREHOLDERS") and
           Enum.member?(audiences, "NEW_SHAREHOLDERS") and
           not is_nil(shareholdings_activity_query) do
        from(q in base_query,
          left_join: shareholder in Gaia.Registers.Shareholding,
          on: shareholder.contact_id == q.id,
          left_join: activity in subquery(shareholdings_activity_query),
          on: activity.shareholding_id == shareholder.id,
          where: not is_nil(activity.id)
        )
      else
        from(q in base_query, where: q.id == -1)
      end

    all_shareholders_query
    |> union_all(^hub_users_query)
    |> union_all(^imported_contacts_query)
    |> union_all(^new_shareholders_query)
  end

  defp filter_contacts_where_states([]), do: dynamic(false)

  defp filter_contacts_where_states(states), do: dynamic([q], q.address_state in ^states)

  defp filter_contacts_where_countries(countries) do
    Enum.reduce(countries, dynamic(false), fn
      "NZ", dynamic ->
        dynamic([q], ^dynamic or q.address_country == "NEW ZEALAND")

      "OTHER", dynamic ->
        dynamic([q], ^dynamic or q.address_country not in ["AUSTRALIA", "NEW ZEALAND"])

      _, dynamic ->
        dynamic
    end)
  end

  defp order_contacts_query(query, orders) do
    Enum.reduce(orders, query, fn
      %{key: "id", value: "asc"}, query_acc ->
        order_by(query_acc, [q], asc: q.id)

      %{key: "id", value: "desc"}, query_acc ->
        order_by(query_acc, [q], desc: q.id)

      %{key: "inserted_at", value: "asc"}, query_acc ->
        order_by(query_acc, [q], asc: q.inserted_at)

      %{key: "inserted_at", value: "desc"}, query_acc ->
        order_by(query_acc, [q], desc: q.inserted_at)

      %{key: "email", value: "asc"}, query_acc ->
        order_by(query_acc, [q], asc: q.email)

      %{key: "email", value: "desc"}, query_acc ->
        order_by(query_acc, [q], desc: q.email)
    end)
  end

  # For comms audience list
  def group_contacts_by_source(company_profile_id) do
    Contact
    |> join(:left, [contact], investor in assoc(contact, :investor))
    |> join(:left, [contact], shareholdings in assoc(contact, :shareholdings))
    |> where(
      [contact],
      not contact.invalidated and contact.company_profile_id == ^company_profile_id
    )
    |> select(
      [contact, investor, shareholdings],
      %{
        shareholder: %{
          count:
            fragment(
              "COUNT(DISTINCT ?) FILTER (WHERE ? IS NOT null AND ? > 0) ",
              contact.id,
              shareholdings.id,
              shareholdings.share_count
            ),
          last_updated_at: fragment("MAX(?) FILTER (WHERE ? IS NOT null)", contact.inserted_at, shareholdings.id)
        },
        hub: %{
          count: fragment("COUNT(DISTINCT ?) FILTER (WHERE ? IS NOT null)", contact.id, investor.id),
          last_updated_at: fragment("MAX(?) FILTER (WHERE ? IS NOT null)", contact.inserted_at, investor.id)
        },
        imported: %{
          count:
            fragment(
              "COUNT(DISTINCT ?) FILTER (WHERE ? IS NOT null)",
              contact.id,
              contact.imported_at
            ),
          last_updated_at:
            fragment(
              "MAX(?) FILTER (WHERE ? IS NOT null)",
              contact.inserted_at,
              contact.imported_at
            )
        }
      }
    )
    |> Repo.one()
  end

  # For comms unsubscriber list
  def list_unsubscribed_contacts(company_profile_id) do
    unsubscribes_query =
      ContactUnsubscribe
      |> where(company_profile_id: ^company_profile_id)
      |> select([cu], %{contact_id: cu.contact_id})
      |> distinct(true)

    global_unsubscribes_query =
      ContactGlobalUnsubscribe
      |> where(company_profile_id: ^company_profile_id)
      |> select([cgu], %{contact_id: cgu.contact_id})

    Contact
    |> join(:left, [contact], cu in subquery(unsubscribes_query), on: cu.contact_id == contact.id)
    |> join(:left, [contact], cgu in subquery(global_unsubscribes_query), on: cgu.contact_id == contact.id)
    |> join(:left, [contact], investor in assoc(contact, :investor))
    |> join(:left, [contact], shareholdings in assoc(contact, :shareholdings))
    |> where([contact], contact.company_profile_id == ^company_profile_id)
    |> where([contact], not contact.invalidated)
    |> where([contact, cu, cgu], not is_nil(cu.contact_id) or not is_nil(cgu.contact_id))
    |> select([contact, ..., investor, shareholdings], %{
      unsubscribed: %{
        count: fragment("COUNT (DISTINCT ?)", contact.id),
        last_updated_at: max(contact.updated_at),
        shareholder: %{
          count:
            fragment(
              "COUNT (DISTINCT ?) FILTER (WHERE ? IS NOT null)",
              contact.id,
              shareholdings.id
            ),
          last_updated_at: fragment("MAX(?) FILTER (WHERE ? IS NOT null)", contact.updated_at, shareholdings.id)
        },
        hub: %{
          count: fragment("COUNT (DISTINCT ?) FILTER (WHERE ? IS NOT null)", contact.id, investor.id),
          last_updated_at: fragment("MAX(?) FILTER (WHERE ? IS NOT null)", contact.updated_at, investor.id)
        },
        imported: %{
          count:
            fragment(
              "COUNT (DISTINCT ?) FILTER (WHERE ? IS NOT null)",
              contact.id,
              contact.imported_at
            ),
          last_updated_at:
            fragment(
              "MAX(?) FILTER (WHERE ? IS NOT null)",
              contact.updated_at,
              contact.imported_at
            )
        }
      }
    })
    |> Repo.one()
  end

  def list_contacts_with_unverified_emails(company_profile_id) do
    Contact
    |> where([c], c.company_profile_id == ^company_profile_id)
    |> where([c], is_nil(c.email_validity_checked_at))
    |> Repo.all()
  end

  @doc """
  Returns `true` if all ids belong to the company.

  ## Examples

      iex> contact_ids_belong_to_company?([1, 2], 1)
      true

  """
  def contact_ids_belong_to_company?(ids, company_profile_id) do
    Contact
    |> where(company_profile_id: ^company_profile_id)
    |> where([contact], contact.id in ^ids)
    |> Repo.aggregate(:count)
    |> Kernel.==(Enum.count(ids))
  end

  @doc """
  Returns the list of contacts_tags.

  ## Examples

      iex> list_contacts_tags()
      [%Tag{}, ...]

  """
  def list_contacts_tags do
    Repo.all(Tag)
  end

  @doc """
  Gets a single tag.

  Raises `Ecto.NoResultsError` if the Tag does not exist.

  ## Examples

      iex> get_tag!(123)
      %Tag{}

      iex> get_tag!(456)
      ** (Ecto.NoResultsError)

  """
  def get_tag!(id), do: Repo.get!(Tag, id)

  @doc """
  Gets a single tag.

  Returns nil if the Tag does not exist.

  Raises `Ecto.MultipleResultsError` if more than one entry found.

  ## Examples

      iex> get_tag_by(%{key: value})
      %Contact{}

      iex> get_tag_by(%{key: value})
      nil

      iex> get_tag_by(%{key: value})
      ** (Ecto.MultipleResultsError)

  """
  def get_tag_by(attrs), do: Repo.get_by(Tag, attrs)

  @doc """
  Creates a tag.

  ## Examples

      iex> create_tag(%{field: value})
      {:ok, %Tag{}}

      iex> create_tag(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_tag(attrs \\ %{}) do
    %Tag{}
    |> Tag.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a tag.

  ## Examples

      iex> update_tag(tag, %{field: new_value})
      {:ok, %Tag{}}

      iex> update_tag(tag, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_tag(%Tag{} = tag, attrs) do
    tag
    |> Tag.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a tag.

  ## Examples

      iex> delete_tag(tag)
      {:ok, %Tag{}}

      iex> delete_tag(tag)
      {:error, %Ecto.Changeset{}}

  """
  def delete_tag(%Tag{} = tag) do
    Repo.delete(tag)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking tag changes.

  ## Examples

      iex> change_tag(tag)
      %Ecto.Changeset{data: %Tag{}}

  """
  def change_tag(%Tag{} = tag, attrs \\ %{}) do
    Tag.changeset(tag, attrs)
  end

  @doc """
  Returns a list of map.

  It includes all created tags ordered by most used.

  ## Examples

    iex> existing_tags_by_company_profile(profile)
    [%{id: "id", invalidated: false, name: "name"}]

  """
  def existing_tags_by_company_profile(%Profile{id: company_profile_id}) do
    Tag
    |> join(:inner, [t], c in assoc(t, :contact))
    |> where(company_profile_id: ^company_profile_id)
    |> group_by([t], [t.name])
    |> order_by([t], desc: count(t.name))
    |> order_by([t], asc: t.name)
    |> select([t], %{id: fragment("'tag-' || ?", t.name), invalidated: false, name: t.name})
    |> Repo.all()
  end

  @doc """
  Returns the list of contacts_notes.

  ## Examples

      iex> list_contacts_notes()
      [%Note{}, ...]

  """
  def list_contacts_notes do
    Repo.all(Note)
  end

  @doc """
  Gets a single note.

  Raises `Ecto.NoResultsError` if the Note does not exist.

  ## Examples

      iex> get_note!(123)
      %Note{}

      iex> get_note!(456)
      ** (Ecto.NoResultsError)

  """
  def get_note!(id), do: Repo.get!(Note, id)

  @doc """
  Gets a single note.

  Returns nil if the Note does not exist.

  Raises `Ecto.MultipleResultsError` if more than one entry found.

  ## Examples

      iex> get_note_by(%{key: value})
      %Note{}

      iex> get_note_by(%{key: value})
      nil

      iex> get_note_by(%{key: value})
      ** (Ecto.MultipleResultsError)

  """
  def get_note_by(attrs), do: Repo.get_by(Note, attrs)

  @doc """
  Creates a note.

  ## Examples

      iex> create_note(%{field: value})
      {:ok, %Note{}}

      iex> create_note(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_note(attrs \\ %{}) do
    %Note{}
    |> Note.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a note.

  ## Examples

      iex> update_note(note, %{field: new_value})
      {:ok, %Note{}}

      iex> update_note(note, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_note(%Note{} = note, attrs) do
    note
    |> Note.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a note.

  ## Examples

      iex> delete_note(note)
      {:ok, %Note{}}

      iex> delete_note(note)
      {:error, %Ecto.Changeset{}}

  """
  def delete_note(%Note{} = note) do
    Repo.delete(note)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking note changes.

  ## Examples

      iex> change_note(note)
      %Ecto.Changeset{data: %Note{}}

  """
  def change_note(%Note{} = note, attrs \\ %{}) do
    Note.changeset(note, attrs)
  end

  @registry_activity "registry_activity"
  @hub_activity "hub_activity"
  @email_activity "email_campaigns"
  @manual_activity "manual_activity"
  @milestone_activity "milestone_activity"
  @doc """
  Returns a contact activities query.

  Activity includes (ordered chronologically):
  - Announcement comment liked by contact
  - Announcement commented by contact
  - Announcement liked by contact
  - Announcement question (survey) answered by contact
  - Shares movement occured
  - Notes or activities manually logged to the contact
  - Emails automatically log to contact
  - Contact registered for a webinar
  - Contact attended a webinar
  """
  # ACTIVITIES FOR CONTACTS WITH INVESTORHUB AND EMAIL (A GIVEN)
  def contact_activities_query(
        %Contact{
          company_profile:
            %Gaia.Companies.Profile{id: company_profile_id, ticker: %Gaia.Markets.Ticker{listing_key: listing_key}} =
              company_profile,
          id: contact_id,
          investor: %Gaia.Investors.User{id: investor_id},
          shareholdings: shareholdings
        } = contact,
        options
      )
      when is_list(shareholdings) do
    company_user_permissions = Map.get(options, :permissions)
    options = Map.delete(options, :permissions)

    # Metadata will contain more information about the activity
    # Please check that we don't send any data we don't want to expose via metadata

    # TODO: remove this once the crm refactor is released
    # for now, no ann/update view events unless the crm refactor is enabled
    contact_activities_query =
      if FeatureFlags.enabled?(:crm_refactor_profile, for: company_profile) do
        listing_key
        |> announcement_comment_likes_query(investor_id)
        |> union_all(^announcement_comments_query(listing_key, investor_id))
        |> union_all(^announcement_likes_query(listing_key, investor_id))
        |> union_all(^announcement_surveys_query(listing_key, investor_id))
        # announcement views added
        |> union_all(^announcement_views_query(company_profile_id, investor_id))
        |> union_all(^update_comment_likes_query(company_profile_id, investor_id))
        |> union_all(^update_comments_query(company_profile_id, investor_id))
        |> union_all(^update_likes_query(company_profile_id, investor_id))
        |> union_all(^update_surveys_query(company_profile_id, investor_id))
        # update views added
        |> union_all(^update_views_query(company_profile_id, investor_id))
        |> maybe_pipe_if_has_permission(
          "registers_shareholdings.admin",
          company_user_permissions,
          &union_all(&1, ^contact_movements_query(contact))
        )
        |> union_all(^contact_notes_query(contact))
        |> union_all(^contact_email_flow_events_query(contact, company_profile_id))
        |> union_all(^contact_email_campaign_events_query(contact, company_profile_id))
        |> union_all(^contact_email_logs_query(contact))
        |> union_all(^webinar_registrations_query(company_profile_id, contact_id))
        |> union_all(^webinar_attendance_query(company_profile_id, contact_id))
        |> union_all(^investorhub_signup_query(contact_id))
        |> subquery()
      else
        listing_key
        |> announcement_comment_likes_query(investor_id)
        |> union_all(^announcement_comments_query(listing_key, investor_id))
        |> union_all(^announcement_likes_query(listing_key, investor_id))
        |> union_all(^announcement_surveys_query(listing_key, investor_id))
        |> union_all(^update_comment_likes_query(company_profile_id, investor_id))
        |> union_all(^update_comments_query(company_profile_id, investor_id))
        |> union_all(^update_likes_query(company_profile_id, investor_id))
        |> union_all(^update_surveys_query(company_profile_id, investor_id))
        |> union_all(^contact_notes_query(contact))
        |> union_all(^contact_movements_query(contact))
        |> union_all(^contact_email_flow_events_query(contact, company_profile_id))
        |> union_all(^contact_email_campaign_events_query(contact, company_profile_id))
        |> union_all(^contact_email_logs_query(contact))
        |> union_all(^webinar_registrations_query(company_profile_id, contact_id))
        |> union_all(^webinar_attendance_query(company_profile_id, contact_id))
        |> union_all(^investorhub_signup_query(contact_id))
        |> subquery()
      end

    Enum.reduce(options, contact_activities_query, fn
      {:filters, filters}, query -> filter_contact_activities(query, filters)
      {:orders, orders}, query -> order_contact_activities(query, orders)
    end)
  end

  # ACTIVITIES FOR CONTACTS WITH NO INVESTORHUB ACCOUNT AND NO EMAIL
  def contact_activities_query(
        %Contact{
          company_profile: %Gaia.Companies.Profile{ticker: %Gaia.Markets.Ticker{listing_key: _listing_key}},
          id: _contact_id,
          investor: nil,
          email: nil,
          shareholdings: shareholdings
        } = contact,
        options
      )
      when is_list(shareholdings) do
    company_user_permissions = Map.get(options, :permissions)
    options = Map.delete(options, :permissions)

    contact_activities_query =
      contact
      |> contact_notes_query()
      |> maybe_pipe_if_has_permission(
        "registers_shareholdings.admin",
        company_user_permissions,
        &union_all(&1, ^contact_movements_query(contact))
      )
      |> subquery()

    Enum.reduce(options, contact_activities_query, fn
      {:filters, filters}, query -> filter_contact_activities(query, filters)
      {:orders, orders}, query -> order_contact_activities(query, orders)
    end)
  end

  # ACTIVITIES FOR CONTACTS WITH NO INVESTORHUB ACCOUNT BUT HAVE EMAIL
  def contact_activities_query(
        %Contact{
          company_profile: %Gaia.Companies.Profile{
            id: company_profile_id,
            ticker: %Gaia.Markets.Ticker{listing_key: _listing_key}
          },
          id: _contact_id,
          investor: nil,
          shareholdings: shareholdings
        } = contact,
        options
      )
      when is_list(shareholdings) do
    company_user_permissions = Map.get(options, :permissions)
    options = Map.delete(options, :permissions)

    contact_activities_query =
      contact
      |> contact_notes_query()
      |> maybe_pipe_if_has_permission(
        "registers_shareholdings.admin",
        company_user_permissions,
        &union_all(&1, ^contact_movements_query(contact))
      )
      |> union_all(^contact_email_flow_events_query(contact, company_profile_id))
      |> union_all(^contact_email_campaign_events_query(contact, company_profile_id))
      |> union_all(^contact_email_logs_query(contact))
      |> subquery()

    Enum.reduce(options, contact_activities_query, fn
      {:filters, filters}, query -> filter_contact_activities(query, filters)
      {:orders, orders}, query -> order_contact_activities(query, orders)
    end)
  end

  defp maybe_pipe_if_has_permission(query, permission, company_user_permissions, expr) do
    if Gaia.Companies.Permission.has_permission?(permission, company_user_permissions) do
      expr.(query)
    else
      query
    end
  end

  def group_activities_by_month(query) do
    from(g in subquery(query),
      group_by: g.month,
      select: %{
        month: g.month,
        categories:
          fragment(
            "jsonb_agg(jsonb_build_object('category', ?, 'activities', ?))",
            g.category,
            g.activities
          )
      },
      order_by: [desc: g.month]
    )
  end

  def group_activities_by_category(query) do
    from(ca in subquery(query),
      group_by: [fragment("date_trunc('month', ?)", ca.timestamp), ca.category],
      select: %{
        month: fragment("date_trunc('month', ?)", ca.timestamp),
        category: ca.category,
        activities: fragment("jsonb_agg(to_jsonb(?))", ca)
      }
    )
  end

  defp filter_contact_activities(query, filters) do
    Enum.reduce(filters, query, fn
      %{key: _, value: ""}, query ->
        query

      %{key: "start_date", value: value}, query ->
        where(query, [q], q.timestamp >= ^value)

      %{key: "end_date", value: value}, query ->
        where(query, [q], q.timestamp <= ^value)

      %{key: "types", value: value}, query ->
        categories = value |> String.split(",") |> Enum.map(&String.downcase/1)

        # TODO: remove the next 20 lines when crm refactor profile is released
        types =
          Enum.reduce(["note", "meeting", "email", "call", "auto-logged-email"], [], fn
            "note", acc ->
              if Enum.member?(categories, "note"), do: acc ++ ["note"], else: acc

            "meeting", acc ->
              if Enum.member?(categories, "meeting"), do: acc ++ ["meeting"], else: acc

            "email", acc ->
              if Enum.member?(categories, "email"), do: acc ++ ["email"], else: acc

            "call", acc ->
              if Enum.member?(categories, "call"), do: acc ++ ["call"], else: acc

            "auto-logged-email", acc ->
              if Enum.member?(categories, "auto-logged-email"),
                do: acc ++ ["auto-logged-email"],
                else: acc

            _, acc ->
              acc
          end)

        where(query, [q], q.category in ^categories or q.type in ^types)

      %{key: "created_by", value: "auto-generated"}, query ->
        # auto-generated activities are those that are not created by a company user manually in athena
        # currently the only activities that are manually created in athena are notes
        company_user_created_types =
          Enum.map(
            Gaia.Contacts.Note.get_contacts_note_types(),
            &(&1 |> Atom.to_string() |> String.downcase())
          )

        where(query, [q], q.type not in ^company_user_created_types)

      %{key: "created_by", value: value}, query ->
        user_ids = value |> String.split(",") |> Enum.filter(&(&1 != "auto-generated"))

        if String.contains?(value, "auto-generated") do
          where(
            query,
            [q],
            fragment(
              "? IS NULL OR (NOT ? IS NULL AND ?->>'id' = ANY(?))",
              q.company_user,
              q.company_user,
              q.company_user,
              ^user_ids
            )
          )
        else
          where(query, [q], fragment("CAST(?->'id' AS TEXT) = ANY(?)", q.company_user, ^user_ids))
        end
    end)
  end

  defp order_contact_activities(query, orders) do
    Enum.reduce(orders, query, fn
      %{key: "id", value: "asc"}, query_acc ->
        order_by(query_acc, [q], asc: q.id)

      %{key: "id", value: "desc"}, query_acc ->
        order_by(query_acc, [q], desc: q.id)

      %{key: "timestamp", value: "asc"}, query_acc ->
        order_by(query_acc, [q], asc: q.timestamp)

      %{key: "timestamp", value: "desc"}, query_acc ->
        order_by(query_acc, [q], desc: q.timestamp)
    end)
  end

  defp announcement_comment_likes_query(listing_key, investor_id) do
    Gaia.Interactions.MediaAnnouncement
    |> join(:inner, [ann], comment in Gaia.Interactions.MediaComment, on: ann.media_id == comment.media_id)
    |> join(:inner, [_, comment], like in assoc(comment, :comment_likes))
    |> join(:inner, [_, _, like], investor in assoc(like, :investor_user))
    |> where([ann], ann.listing_key == ^String.upcase(listing_key))
    |> where([_, _, _, investor], investor.id == ^investor_id)
    |> where([_, _, like, _], like.like == true)
    |> select([ann, comment, like, investor], %{
      id: fragment("'announcement-comment-like-' || ?", like.id),
      metadata:
        fragment(
          "json_build_object('announcement', ?, 'announcement_comment', ?, 'announcement_comment_like', ?, 'investor', ?)",
          fragment(
            "json_build_object('id', ?, 'listing_key', ?, 'header', ?, 'market_sensitive', ?, 'summary', ?)",
            ann.id,
            ann.listing_key,
            ann.header,
            ann.market_sensitive,
            ann.summary
          ),
          comment,
          like,
          fragment(
            "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'username', ?, 'email', ?)",
            investor.id,
            investor.first_name,
            investor.last_name,
            investor.username,
            investor.email
          )
        ),
      timestamp: like.inserted_at,
      type: "announcement-comment-like",
      category: @hub_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  defp announcement_comments_query(listing_key, investor_id) do
    Gaia.Interactions.MediaAnnouncement
    |> join(:inner, [ann], comment in Gaia.Interactions.MediaComment, on: ann.media_id == comment.media_id)
    |> join(:inner, [_, comment], investor in assoc(comment, :investor_user))
    |> where([ann], ann.listing_key == ^String.upcase(listing_key))
    |> where([_, _, investor], investor.id == ^investor_id)
    |> select([ann, comment, investor], %{
      id: fragment("'announcement-comment-' || ?", comment.id),
      metadata:
        fragment(
          "json_build_object('announcement', ?, 'announcement_comment', ?, 'investor', ?)",
          fragment(
            "json_build_object('id', ?, 'listing_key', ?, 'header', ?, 'market_sensitive', ?, 'summary', ?)",
            ann.id,
            ann.listing_key,
            ann.header,
            ann.market_sensitive,
            ann.summary
          ),
          comment,
          fragment(
            "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'username', ?, 'email', ?)",
            investor.id,
            investor.first_name,
            investor.last_name,
            investor.username,
            investor.email
          )
        ),
      timestamp: comment.inserted_at,
      type: "announcement-comment",
      category: @hub_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  defp announcement_likes_query(listing_key, investor_id) do
    Gaia.Interactions.MediaAnnouncement
    |> join(:inner, [ann], like in Gaia.Interactions.MediaLike, on: ann.media_id == like.media_id)
    |> join(:inner, [ann, like], investor in assoc(like, :investor_user))
    |> where([ann], ann.listing_key == ^String.upcase(listing_key))
    |> where([_, _, investor], investor.id == ^investor_id)
    |> where([_, like, _], like.like == true)
    |> select([ann, like, investor], %{
      id: fragment("'announcement-like-' || ?", like.id),
      metadata:
        fragment(
          "json_build_object('announcement', ?, 'announcement_like', ?, 'investor', ?)",
          fragment(
            "json_build_object('id', ?, 'listing_key', ?, 'header', ?, 'market_sensitive', ?, 'summary', ?)",
            ann.id,
            ann.listing_key,
            ann.header,
            ann.market_sensitive,
            ann.summary
          ),
          like,
          fragment(
            "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'username', ?, 'email', ?)",
            investor.id,
            investor.first_name,
            investor.last_name,
            investor.username,
            investor.email
          )
        ),
      timestamp: like.inserted_at,
      type: "announcement-like",
      category: @hub_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  defp announcement_surveys_query(listing_key, investor_id) do
    Gaia.Interactions.MediaSurveyAnswer
    |> join(:inner, [survey], ann in Gaia.Interactions.MediaAnnouncement, on: survey.media_id == ann.media_id)
    |> join(:inner, [survey], investor in assoc(survey, :investor_user))
    |> where([_, ann], ann.listing_key == ^String.upcase(listing_key))
    |> where([_, _, investor], investor.id == ^investor_id)
    |> group_by([_, ann], ann.id)
    |> select([survey, ann], %{
      announcement_id: ann.id,
      investor_user_id: type(^investor_id, :integer),
      surveys: fragment("json_agg(?)", survey),
      timestamp: min(survey.inserted_at)
    })
    |> subquery()
    |> join(:inner, [q], ann in Gaia.Interactions.MediaAnnouncement, on: ann.id == q.announcement_id)
    |> join(:inner, [q], investor in Gaia.Investors.User, on: investor.id == q.investor_user_id)
    |> select([q, ann, investor], %{
      id: fragment("'announcement-survey-' || ?", ann.id),
      metadata:
        fragment(
          "json_build_object('announcement', ?, 'announcement_surveys', ?, 'investor', ?)",
          fragment(
            "json_build_object('id', ?, 'listing_key', ?, 'header', ?, 'market_sensitive', ?, 'summary', ?)",
            ann.id,
            ann.listing_key,
            ann.header,
            ann.market_sensitive,
            ann.summary
          ),
          q,
          fragment(
            "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'username', ?, 'email', ?)",
            investor.id,
            investor.first_name,
            investor.last_name,
            investor.username,
            investor.email
          )
        ),
      timestamp: q.timestamp,
      type: "announcement-survey",
      category: @hub_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  def announcement_views_query(company_profile_id, investor_id) do
    Gaia.Tracking.InvestorHub
    |> where([ih], ih.company_profile_id == ^company_profile_id)
    |> where([ih], ih.investor_user_id == ^investor_id)
    |> where([ih], like(ih.event, ^"%announcement_page_viewed%"))
    |> join(
      :inner,
      [ih],
      ann in Gaia.Interactions.MediaAnnouncement,
      on: ann.id == fragment("CAST(split_part(?, '_', 1) AS bigint)", ih.event)
    )
    |> join(
      :inner,
      [ih, ann],
      investor in Gaia.Investors.User,
      on: investor.id == ih.investor_user_id
    )
    |> select(
      [ih, ann, investor],
      %{
        id: fragment("'announcement-view-' || ?", ann.id),
        metadata:
          fragment(
            "json_build_object('announcement', ?, 'investor', ?)",
            fragment(
              "json_build_object('id', ?, 'listing_key', ?, 'header', ?, 'market_sensitive', ?, 'summary', ?)",
              ann.id,
              ann.listing_key,
              ann.header,
              ann.market_sensitive,
              ann.summary
            ),
            fragment(
              "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'username', ?, 'email', ?)",
              investor.id,
              investor.first_name,
              investor.last_name,
              investor.username,
              investor.email
            )
          ),
        timestamp: ih.inserted_at,
        type: "announcement-view",
        category: @hub_activity,
        company_user: fragment("CAST(? AS JSON)", nil)
      }
    )
  end

  defp update_comment_likes_query(company_profile_id, investor_id) do
    Gaia.Interactions.MediaUpdate
    |> join(:inner, [update], comment in Gaia.Interactions.MediaComment, on: update.media_id == comment.media_id)
    |> join(:inner, [_, comment], like in assoc(comment, :comment_likes))
    |> join(:inner, [_, _, like], investor in assoc(like, :investor_user))
    |> where([update], update.company_profile_id == ^company_profile_id)
    |> where([_, _, _, investor], investor.id == ^investor_id)
    |> where([_, _, like, _], like.like == true)
    |> select([update, comment, like, investor], %{
      id: fragment("'update-comment-like-' || ?", like.id),
      metadata:
        fragment(
          "json_build_object('update', ?, 'update_comment', ?, 'update_comment_like', ?, 'investor', ?)",
          fragment(
            "json_build_object('id', ?, 'title', ?)",
            update.id,
            update.title
          ),
          comment,
          like,
          fragment(
            "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'username', ?, 'email', ?)",
            investor.id,
            investor.first_name,
            investor.last_name,
            investor.username,
            investor.email
          )
        ),
      timestamp: like.inserted_at,
      type: "update-comment-like",
      category: @hub_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  defp update_comments_query(company_profile_id, investor_id) do
    Gaia.Interactions.MediaUpdate
    |> join(:inner, [update], comment in Gaia.Interactions.MediaComment, on: update.media_id == comment.media_id)
    |> join(:inner, [_, comment], investor in assoc(comment, :investor_user))
    |> where([update], update.company_profile_id == ^company_profile_id)
    |> where([_, _, investor], investor.id == ^investor_id)
    |> select([update, comment, investor], %{
      id: fragment("'update-comment-' || ?", comment.id),
      metadata:
        fragment(
          "json_build_object('update', ?, 'update_comment', ?, 'investor', ?)",
          fragment(
            "json_build_object('id', ?, 'title', ?)",
            update.id,
            update.title
          ),
          comment,
          fragment(
            "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'username', ?, 'email', ?)",
            investor.id,
            investor.first_name,
            investor.last_name,
            investor.username,
            investor.email
          )
        ),
      timestamp: comment.inserted_at,
      type: "update-comment",
      category: @hub_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  defp update_likes_query(company_profile_id, investor_id) do
    Gaia.Interactions.MediaUpdate
    |> join(:inner, [update], like in Gaia.Interactions.MediaLike, on: update.media_id == like.media_id)
    |> join(:inner, [update, like], investor in assoc(like, :investor_user))
    |> where([update], update.company_profile_id == ^company_profile_id)
    |> where([_, _, investor], investor.id == ^investor_id)
    |> where([_, like, _], like.like == true)
    |> select([update, like, investor], %{
      id: fragment("'update-like-' || ?", like.id),
      metadata:
        fragment(
          "json_build_object('update', ?, 'update_like', ?, 'investor', ?)",
          fragment(
            "json_build_object('id', ?, 'title', ?)",
            update.id,
            update.title
          ),
          like,
          fragment(
            "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'username', ?, 'email', ?)",
            investor.id,
            investor.first_name,
            investor.last_name,
            investor.username,
            investor.email
          )
        ),
      timestamp: like.inserted_at,
      type: "update-like",
      category: @hub_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  defp update_surveys_query(company_profile_id, investor_id) do
    Gaia.Interactions.MediaSurveyAnswer
    |> join(:inner, [survey], update in Gaia.Interactions.MediaUpdate, on: survey.media_id == update.media_id)
    |> join(:inner, [survey], investor in assoc(survey, :investor_user))
    |> where([_, update], update.company_profile_id == ^company_profile_id)
    |> where([_, _, investor], investor.id == ^investor_id)
    |> group_by([_, update], update.id)
    |> select([survey, update], %{
      update_id: update.id,
      investor_user_id: type(^investor_id, :integer),
      surveys: fragment("json_agg(?)", survey),
      timestamp: min(survey.inserted_at)
    })
    |> subquery()
    |> join(:inner, [q], update in Gaia.Interactions.MediaUpdate, on: update.id == q.update_id)
    |> join(:inner, [q], investor in Gaia.Investors.User, on: investor.id == q.investor_user_id)
    |> select([q, update, investor], %{
      id: fragment("'update-survey-' || ?", update.id),
      metadata:
        fragment(
          "json_build_object('update', ?, 'update_surveys', ?, 'investor', ?)",
          fragment(
            "json_build_object('id', ?, 'title', ?)",
            update.id,
            update.title
          ),
          q,
          fragment(
            "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'username', ?, 'email', ?)",
            investor.id,
            investor.first_name,
            investor.last_name,
            investor.username,
            investor.email
          )
        ),
      timestamp: q.timestamp,
      type: "update-survey",
      category: @hub_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  def update_views_query(company_profile_id, investor_id) do
    Gaia.Tracking.InvestorHub
    |> where([ih], ih.company_profile_id == ^company_profile_id)
    |> where([ih], ih.investor_user_id == ^investor_id)
    |> where([ih], like(ih.event, ^"%activity_update_page_viewed%"))
    |> join(
      :inner,
      [ih],
      update in Gaia.Interactions.MediaUpdate,
      on: update.id == fragment("CAST(split_part(?, '_', 1) AS bigint)", ih.event)
    )
    |> join(
      :inner,
      [ih, update],
      investor in Gaia.Investors.User,
      on: investor.id == ih.investor_user_id
    )
    |> select(
      [ih, update, investor],
      %{
        id: fragment("'update-view-' || ?", update.id),
        metadata:
          fragment(
            "json_build_object('update', ?, 'investor', ?)",
            fragment(
              "json_build_object('id', ?, 'title', ?, 'slug', ?)",
              update.id,
              update.title,
              update.slug
            ),
            fragment(
              "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'username', ?, 'email', ?)",
              investor.id,
              investor.first_name,
              investor.last_name,
              investor.username,
              investor.email
            )
          ),
        timestamp: ih.inserted_at,
        type: "update-view",
        category: @hub_activity,
        company_user: fragment("CAST(? AS JSON)", nil)
      }
    )
  end

  defp webinar_registrations_query(company_profile_id, contact_id) do
    Gaia.Webinars.Webinar
    |> join(:inner, [w], attendee in Gaia.Webinars.Attendee, on: attendee.webinar_id == w.id)
    |> join(:inner, [w, attendee], investor in Gaia.Investors.User, on: attendee.investor_user_id == investor.id)
    |> where([w, attendee, investor], investor.contact_id == ^contact_id)
    |> where([w], w.company_profile_id == ^company_profile_id)
    |> select([w, attendee, investor], %{
      id: fragment("'webinar-registration-' || ?", attendee.id),
      metadata:
        fragment(
          "json_build_object('webinar', ?, 'attendee', ?, 'investor', ?)",
          fragment(
            "json_build_object('id', ?, 'title', ?, 'start_time', ?)",
            w.id,
            w.title,
            w.start_time
          ),
          attendee,
          fragment(
            "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'email', ?)",
            investor.id,
            investor.first_name,
            investor.last_name,
            investor.email
          )
        ),
      timestamp: attendee.inserted_at,
      type: "webinar-registration",
      category: @hub_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  defp webinar_attendance_query(company_profile_id, contact_id) do
    Gaia.Webinars.Webinar
    |> join(:inner, [w], attendee in Gaia.Webinars.Attendee, on: attendee.webinar_id == w.id)
    |> join(:inner, [w, attendee], investor in Gaia.Investors.User, on: attendee.investor_user_id == investor.id)
    |> where([w, attendee, investor], investor.contact_id == ^contact_id)
    |> where([w], w.company_profile_id == ^company_profile_id)
    |> where([w, attendee], attendee.attended == true and not is_nil(w.started_broadcasting_at))
    |> select([w, attendee, investor], %{
      id: fragment("'webinar-attendance-' || ?", attendee.id),
      metadata:
        fragment(
          "json_build_object('webinar', ?, 'attendee', ?, 'investor', ?)",
          fragment(
            "json_build_object('id', ?, 'title', ?, 'start_time', ?, 'started_broadcasting_at', ?)",
            w.id,
            w.title,
            w.start_time,
            w.started_broadcasting_at
          ),
          attendee,
          fragment(
            "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'email', ?)",
            investor.id,
            investor.first_name,
            investor.last_name,
            investor.email
          )
        ),
      timestamp: w.started_broadcasting_at,
      type: "webinar-attendance",
      category: @hub_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  defp investorhub_signup_query(contact_id) do
    Gaia.Investors.User
    |> join(:inner, [investor], contact in assoc(investor, :contact))
    |> join(:left, [investor, contact], utm_link in Gaia.Tracking.UtmLink,
      on: investor.source_type == "utm_link" and investor.source_id == utm_link.id
    )
    |> where([investor], investor.contact_id == ^contact_id)
    |> select(
      [investor, _contact, utm_link],
      %{
        id: fragment("'hub-signup-' || ?", investor.id),
        metadata:
          fragment(
            "json_build_object('investor', ?, 'utm_link', ?)",
            fragment(
              "json_build_object('email', ?, 'username', ?, 'inserted_at', ?)",
              investor.email,
              investor.username,
              investor.inserted_at
            ),
            fragment(
              "json_build_object('destination_url', ?, 'id', ?, 'utm_campaign', ?, 'utm_source', ?, 'utm_medium', ?, 'is_user_generated', ?)",
              utm_link.destination_url,
              utm_link.id,
              utm_link.utm_campaign,
              utm_link.utm_source,
              utm_link.utm_medium,
              utm_link.is_user_generated
            )
          ),
        timestamp: investor.inserted_at,
        type: "hub-signup",
        category: @milestone_activity,
        company_user: fragment("CAST(? AS JSON)", nil)
      }
    )
  end

  # We didn't know the time of when the settlement occurs.
  # Set the timestamp to the 10 am of the settled_date (UTC).
  # Equivalent to 8 or 9 pm Australian time.
  defp contact_movements_query(%Contact{shareholdings: shareholdings}) when is_list(shareholdings) do
    Gaia.Registers.ShareMovement
    |> join(:inner, [sm], sh in assoc(sm, :shareholding))
    |> where([sm], sm.shareholding_id in ^Enum.map(shareholdings, & &1.id))
    |> select([sm, sh], %{
      id: fragment("'movement-' || ?", sm.id),
      metadata:
        fragment(
          "json_build_object('shareholding', ?, 'share_movement', ?)",
          fragment(
            "json_build_object('id', ?, 'account_name', ?, 'broker_pid', ?)",
            sh.id,
            sh.account_name,
            sh.broker_pid
          ),
          fragment(
            "json_build_object('id', ?, 'opening_balance', ?, 'movement', ?, 'closing_balance', ?, 'settled_at', ?, 'movement_type', ?, 'broker_pid', ?, 'transaction_price', ?)",
            sm.id,
            sm.opening_balance,
            sm.movement,
            sm.closing_balance,
            sm.settled_at,
            sm.movement_type,
            sm.broker_pid,
            sm.transaction_price
          )
        ),
      timestamp: fragment("? + '10:00:00'::time", sm.settled_at),
      type: "movement",
      category: @registry_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  # Show all notes as they can be created by anyone from the company
  defp contact_notes_query(%Contact{id: contact_id}) do
    Note
    |> join(:inner, [note], user in assoc(note, :company_user))
    |> join(:inner, [note], contact in assoc(note, :contact))
    |> where([note], not note.invalidated)
    |> where([_, _, contact], contact.id == ^contact_id)
    |> select([note, user], %{
      id: fragment("CAST(? AS text) || '-' || ?", note.type, note.id),
      metadata:
        fragment(
          "json_build_object('details', ?, 'user', ?)",
          note,
          fragment(
            "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'email', ?)",
            user.id,
            user.first_name,
            user.last_name,
            user.email
          )
        ),
      timestamp: note.occured_at,
      type: fragment("CAST(? AS text)", note.type),
      category: @manual_activity,
      company_user:
        fragment(
          "json_build_object('id', ?, 'first_name', ?, 'last_name', ?, 'email', ?)",
          user.id,
          user.first_name,
          user.last_name,
          user.email
        )
    })
  end

  # Some weird issue with the number of \r and \n for some emails do not match what we see in the UI
  # Decided to format to max two new lines
  defp contact_email_logs_query(%Contact{id: contact_id}) do
    EmailLog
    |> where(contact_id: ^contact_id)
    |> select([log], %{
      id: fragment("'auto-logged-email-' || ?", log.id),
      metadata:
        fragment(
          "json_build_object('subject', ?, 'text_content', REGEXP_REPLACE(REGEXP_REPLACE(?, '\r\n', '\n', 'g'), '([\r|\n]){2,}', '\n\n', 'g'))",
          log.subject,
          log.text_content
        ),
      timestamp: log.updated_at,
      type: "auto-logged-email",
      category: @manual_activity,
      company_user: fragment("CAST(? AS JSON)", nil)
    })
  end

  defp contact_email_flow_events_query(%Contact{email: email}, company_profile_id) do
    EmailEvent
    |> join(:inner, [event], tracking_email in assoc(event, :email))
    |> join(:inner, [event, tracking_email], email_recipient in EmailRecipient,
      on: email_recipient.tracking_email_id == tracking_email.id
    )
    |> join(
      :inner,
      [event, tracking_email, email_recipient],
      comms_email in assoc(email_recipient, :email)
    )
    |> join(
      :inner,
      [event, tracking_email, email_recipient, email],
      media in assoc(email, :media)
    )
    |> join(
      :left,
      [event, tracking_email, email_recipient, comms_email, media],
      announcement in assoc(media, :media_announcement)
    )
    |> join(
      :left,
      [event, tracking_email, email_recipient, comms_email, media, announcement],
      media_update in assoc(media, :media_update)
    )
    |> where(
      [
        _event,
        tracking_email,
        _email_recipient,
        _comms_email,
        _media,
        _announcement,
        _media_update
      ],
      tracking_email.company_profile_id == ^company_profile_id and tracking_email.to == ^email
    )
    |> select(
      [
        event,
        tracking_email,
        email_recipient,
        _comms_email,
        _media,
        announcement,
        media_update
      ],
      %{
        id: fragment("'email-event-' || ?", event.id),
        metadata:
          fragment(
            "json_build_object('event', ?, 'email', ?, 'email_recipient', ?, 'announcement', ?, 'update', ?)",
            event,
            fragment(
              "json_build_object('id', ?, 'to', ?, 'subject', ?, 'sent', ?)",
              tracking_email.id,
              tracking_email.to,
              tracking_email.subject,
              tracking_email.inserted_at
            ),
            email_recipient,
            fragment(
              "json_build_object('id', ?)",
              announcement.id
            ),
            fragment(
              "json_build_object('id', ?)",
              media_update.id
            )
          ),
        timestamp: event.inserted_at,
        type: "email-event-flow",
        category: @email_activity,
        company_user: fragment("CAST(? AS JSON)", nil)
      }
    )
  end

  # Email events log (general campaigns)
  defp contact_email_campaign_events_query(%Contact{email: email}, company_profile_id) do
    EmailEvent
    |> join(:inner, [event], tracking_email in assoc(event, :email))
    |> join(:inner, [event, tracking_email], email_recipient in EmailRecipient,
      on: email_recipient.tracking_email_id == tracking_email.id
    )
    |> join(
      :inner,
      [event, tracking_email, email_recipient],
      comms_email in assoc(email_recipient, :email)
    )
    |> where(
      [
        _event,
        tracking_email,
        _email_recipient,
        comms_email
      ],
      tracking_email.company_profile_id == ^company_profile_id and tracking_email.to == ^email and
        is_nil(comms_email.media_id)
    )
    |> select(
      [
        event,
        tracking_email,
        email_recipient,
        comms_email
      ],
      %{
        id: fragment("'email-event-' || ?", event.id),
        metadata:
          fragment(
            "json_build_object('event', ?, 'email', ?, 'email_recipient', ?, 'campaign', ?)",
            event,
            fragment(
              "json_build_object('id', ?, 'to', ?, 'subject', ?, 'sent', ?)",
              tracking_email.id,
              tracking_email.to,
              tracking_email.subject,
              tracking_email.inserted_at
            ),
            email_recipient,
            fragment(
              "json_build_object('id', ?)",
              comms_email.id
            )
          ),
        timestamp: event.inserted_at,
        type: "email-event-campaign",
        category: @email_activity,
        company_user: fragment("CAST(? AS JSON)", nil)
      }
    )
  end

  # Latest engagement activity for a contact with an investorhub account
  def get_contact_latest_engagement_activity(
        %Contact{
          id: contact_id,
          investor: %Gaia.Investors.User{id: investor_id},
          company_profile:
            %Gaia.Companies.Profile{id: company_profile_id, ticker: %Gaia.Markets.Ticker{listing_key: listing_key}} =
              company_profile
        } = contact
      ) do
    if FeatureFlags.enabled?(:crm_refactor_profile, for: company_profile) do
      listing_key
      |> announcement_comment_likes_query(investor_id)
      |> union_all(^announcement_comments_query(listing_key, investor_id))
      |> union_all(^announcement_likes_query(listing_key, investor_id))
      |> union_all(^announcement_surveys_query(listing_key, investor_id))
      |> union_all(^announcement_views_query(company_profile_id, investor_id))
      |> union_all(^update_comment_likes_query(company_profile_id, investor_id))
      |> union_all(^update_comments_query(company_profile_id, investor_id))
      |> union_all(^update_likes_query(company_profile_id, investor_id))
      |> union_all(^update_surveys_query(company_profile_id, investor_id))
      |> union_all(^update_views_query(company_profile_id, investor_id))
      |> union_all(^contact_email_flow_events_query(contact, company_profile_id))
      |> union_all(^contact_email_campaign_events_query(contact, company_profile_id))
      |> union_all(^webinar_registrations_query(company_profile_id, contact_id))
      |> union_all(^webinar_attendance_query(company_profile_id, contact_id))
      |> union_all(^investorhub_signup_query(contact_id))
      |> subquery()
      |> order_by([q], desc: q.timestamp)
      |> limit(1)
      |> Repo.one()
    else
      []
    end
  end

  # Latest engagement activity for a contact without an investorhub account
  def get_contact_latest_engagement_activity(
        %Contact{
          id: contact_id,
          company_profile:
            %Gaia.Companies.Profile{id: company_profile_id, ticker: %Gaia.Markets.Ticker{listing_key: _listing_key}} =
              company_profile
        } = contact
      ) do
    if FeatureFlags.enabled?(:crm_refactor_profile, for: company_profile) do
      contact
      |> contact_email_flow_events_query(company_profile_id)
      |> union_all(^contact_email_campaign_events_query(contact, company_profile_id))
      |> union_all(^webinar_registrations_query(company_profile_id, contact_id))
      |> union_all(^webinar_attendance_query(company_profile_id, contact_id))
      |> union_all(^investorhub_signup_query(contact_id))
      |> subquery()
      |> order_by([q], desc: q.timestamp)
      |> limit(1)
      |> Repo.one()
    else
      []
    end
  end

  @doc """
  Returns a contact's share movements and daily holdings during the requested period.

  The share movements and daily holdings of a contact is from all linked shareholdings.
  """
  def get_contact_movements_and_daily_holdings(
        %Contact{id: contact_id, company_profile_id: company_profile_id, shareholdings: shareholdings},
        %Date{} = start_date,
        %Date{} = end_date,
        opts \\ []
      )
      when is_list(shareholdings) do
    shareholding_ids = Enum.map(shareholdings, & &1.id)

    default = [sort_order: :asc]
    options = Keyword.merge(default, opts)
    sort_order = Keyword.get(options, :sort_order)

    # Get share movements for all of the shareholdings within the period
    movements =
      Gaia.Registers.ShareMovement
      |> join(:inner, [sm], sh in assoc(sm, :shareholding))
      |> where([sm, sh], sm.shareholding_id in ^shareholding_ids)
      |> where([sm, sh], sm.settled_at >= ^start_date)
      |> where([sm, sh], sm.settled_at <= ^end_date)
      |> order_by([sm, sh], [{^sort_order, sm.settled_at}, asc: sm.id])
      |> select([sm, sh], %{
        id: sm.id,
        account_name: sh.account_name,
        closing_balance: sm.closing_balance,
        movement: sm.movement,
        opening_balance: sm.opening_balance,
        settled_at: sm.settled_at,
        movement_type: sm.movement_type,
        transaction_price: sm.transaction_price,
        estimated_price: sm.estimated_price
      })
      |> Repo.all()

    # Get the total starting balance of all of the shareholdings at the start of the period
    # The starting balance at the start_date is same as closing balance of the previous day
    starting_balance =
      Gaia.Registers.DailyHolding
      |> where([dh], dh.shareholding_id in ^shareholding_ids)
      |> where([dh], dh.date == ^Timex.shift(start_date, days: -1))
      |> select([dh], type(sum(dh.balance), :integer))
      |> Repo.one()
      |> Kernel.||(0)

    # Map date to the total movements for all shareholdings for the day
    net_movements_mapping =
      Gaia.Registers.ShareMovement
      |> where([sm], sm.shareholding_id in ^shareholding_ids)
      |> where([sm], sm.settled_at >= ^start_date)
      |> where([sm], sm.settled_at <= ^end_date)
      |> group_by([sm], sm.settled_at)
      |> select([sm], %{
        settled_at: sm.settled_at,
        # Note this issue - postgres treats bigint as a decimal rather than integer, so needs to be transformed to integer to be added later
        # https://elixirforum.com/t/ecto-is-returning-decimal-from-sum-1-after-changing-column-type-from-int-to-bigint-in-postgres/45894
        net_movements: type(sum(sm.movement), :integer)
      })
      |> Repo.all()
      |> Enum.reduce(%{}, &Map.put(&2, &1.settled_at, &1.net_movements))

    first_daily_holding = %{
      id: "#{contact_id}-#{start_date}",
      date: start_date,
      balance: starting_balance
    }

    # Construct the daily holding for the period
    # The end date for daily holding should not be greater than the latest imported date
    # We only show daily holding up until the latest imported date
    latest_imported_date =
      Gaia.Registers.get_max_share_movement_date_for_company(company_profile_id)

    daily_holdings =
      [from: start_date, until: end_date, left_open: true, right_open: false]
      |> Timex.Interval.new()
      |> Enum.reduce([first_daily_holding], fn datetime, [%{balance: prev_balance} | _] = acc ->
        date = Timex.to_date(datetime)

        [
          %{
            id: "#{contact_id}-#{date}",
            date: date,
            balance: prev_balance + Map.get(net_movements_mapping, date, 0)
          }
          | acc
        ]
      end)
      |> Enum.reverse()
      |> Enum.filter(fn %{date: date} ->
        Date.compare(date, latest_imported_date) in [:lt, :eq]
      end)

    {movements, daily_holdings}
  end

  def amplify_investors_query(company_profile_id, args) do
    # Value is nil or string
    search_phrase =
      args
      |> Map.get(:search_phrase)
      |> case do
        input when is_binary(input) ->
          case String.trim(input) do
            "" -> nil
            trimmed_input -> "%#{trimmed_input}%"
          end

        _ ->
          nil
      end

    # If empty list is provided, do not filter by tags
    search_tags_input = Map.get(args, :search_tags)

    search_tags =
      if is_list(search_tags_input) and Enum.count(search_tags_input) > 0 do
        Enum.map(search_tags_input, &String.downcase(String.trim(&1)))
      end

    amplify_investors_main_query(company_profile_id, search_phrase, search_tags)
  end

  defp amplify_investors_main_query(company_profile_id, search_phrase, search_tags) do
    query =
      Contact
      |> where([contact], contact.company_profile_id == ^company_profile_id)
      |> where([contact], not contact.invalidated)

    query =
      case search_phrase do
        nil -> query
        _ -> amplify_investors_search_phrase_query(query, company_profile_id, search_phrase)
      end

    query =
      search_tags
      |> case do
        nil -> query
        _ -> amplify_investors_search_tags_query(query, company_profile_id, search_tags)
      end
      |> order_by([q], asc: q.id)
      |> distinct(true)
      |> select([contact], %{
        id: fragment("'contact-' || ?", contact.id),
        contact_id: contact.id,
        shareholding_id: type(^nil, :integer),
        type: "contact"
      })
      |> subquery()

    # When searching for tags, do not return shareholdings as they don't have tags
    {search_phrase, search_tags}
    |> case do
      {_, search_tags_input} when not is_nil(search_tags_input) ->
        query

      {nil, _} ->
        query

      {_, _} ->
        shareholdings_query =
          amplify_investors_shareholdings_query(company_profile_id, search_phrase)

        query
        |> union_all(^shareholdings_query)
        |> subquery()
    end
    |> join(:left, [q], c in Contact, on: c.id == q.contact_id and not c.invalidated)
    |> join(:left, [q], sh in Gaia.Registers.Shareholding, on: sh.id == q.shareholding_id)
    |> order_by([q], asc: q.type, asc: q.contact_id, asc: q.shareholding_id)
    |> select([q, c, sh], %{
      id: q.id,
      contact: c,
      shareholding: sh,
      type: q.type
    })
  end

  defp amplify_investors_search_phrase_query(query, company_profile_id, search_phrase) do
    query
    |> join(:left, [contact], i in assoc(contact, :investor),
      as: :investor,
      on: i.company_profile_id == ^company_profile_id
    )
    |> join(:left, [contact], sh in assoc(contact, :shareholdings),
      as: :shareholding,
      on: sh.company_profile_id == ^company_profile_id and not is_nil(sh.contact_id)
    )
    |> where(
      [contact, investor: investor, shareholding: shareholding],
      ilike(contact.email, ^search_phrase) or
        ilike(
          fragment(
            "? || ' ' || ?",
            coalesce(contact.first_name, ""),
            coalesce(contact.last_name, "")
          ),
          ^search_phrase
        ) or
        ilike(investor.email, ^search_phrase) or
        ilike(
          fragment(
            "? || ' ' || ?",
            coalesce(investor.first_name, ""),
            coalesce(investor.last_name, "")
          ),
          ^search_phrase
        ) or
        ilike(investor.username, ^search_phrase) or
        ilike(shareholding.account_name, ^search_phrase) or
        ilike(shareholding.email, ^search_phrase)
    )
  end

  defp amplify_investors_search_tags_query(query, company_profile_id, search_tags) do
    query
    |> join(:left, [contact], t in assoc(contact, :tags),
      as: :tag,
      on: t.company_profile_id == ^company_profile_id
    )
    |> where([tag: tag], tag.name in ^search_tags)
  end

  # This is the shareholdings part for amplify_investors_query
  defp amplify_investors_shareholdings_query(company_profile_id, search_phrase) do
    Gaia.Registers.Shareholding
    |> where(company_profile_id: ^company_profile_id)
    |> where(
      [shareholding],
      ilike(shareholding.account_name, ^search_phrase) or
        ilike(shareholding.email, ^search_phrase)
    )
    |> select([shareholding], %{
      id: fragment("'shareholding-' || ?", shareholding.id),
      contact_id: type(^nil, :integer),
      shareholding_id: shareholding.id,
      type: "shareholding"
    })
  end

  def upsert_custom_contacts(
        custom_contacts,
        audience_tags,
        is_global_unsubscribe,
        unsubscribe_scopes,
        apply_subscription_to_new_contact_only,
        company_profile_id,
        company_profile_user_id,
        %Gaia.Companies.User{id: company_user_id} = company_user
      ) do
    utc_now = NaiveDateTime.utc_now(:second)

    # filter out contacts that dont have a good email
    # it is ok if they have no number, and if the number contains + () or numbers
    custom_contacts =
      Enum.filter(
        custom_contacts,
        &(Regex.match?(~r/^[^\s]+@[^\s]+$/, &1.email) &&
            (!Map.has_key?(&1, :phone_number) || String.length(&1.phone_number) == 0 ||
               Regex.match?(~r/^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\/\.0-9]*$/, &1.phone_number)))
      )

    Ecto.Multi.new()
    # Upsert custom contacts
    |> Ecto.Multi.insert_all(
      :upsert_custom_contacts,
      Contact,
      Enum.map(
        custom_contacts,
        &(&1
          |> Map.merge(%{
            company_profile_id: company_profile_id,
            email:
              &1
              |> Map.get(:email)
              |> clean_upsert_custom_contacts_string_input()
              |> String.downcase(),
            first_name: &1 |> Map.get(:first_name) |> clean_upsert_custom_contacts_string_input(),
            last_name: &1 |> Map.get(:last_name) |> clean_upsert_custom_contacts_string_input(),
            imported_at: utc_now,
            inserted_at: utc_now,
            updated_at: utc_now,
            lead_identified_at: utc_now,
            creator_name: Gaia.Companies.User.build_creator_name(company_user),
            creator_user_id: company_user_id,
            contact_source: :bulk_import
          })
          |> Enum.filter(fn {_key, value} -> !is_nil(value) end)
          |> Map.new())
      ),
      on_conflict:
        {:replace_all_except,
         [
           :id,
           :inserted_at,
           :imported_at,
           :lead_converted_at,
           :lead_identified_at,
           :creator_user_id,
           :creator_name,
           :contact_source
         ]},
      conflict_target: {:unsafe_fragment, ~s<("company_profile_id", "email") WHERE invalidated IS FALSE>},
      returning: true
    )
    |> prep_contact_tags(
      :upsert_custom_contacts,
      audience_tags,
      company_profile_id,
      company_profile_user_id,
      utc_now
    )
    |> Ecto.Multi.run(:bulk_unsubscribe_contacts, fn _repo,
                                                     %{
                                                       upsert_custom_contacts: {_num_of_upserted_contacts, contacts}
                                                     } ->
      bulk_unsubscribe_contacts(
        contacts,
        is_global_unsubscribe,
        unsubscribe_scopes,
        apply_subscription_to_new_contact_only,
        company_profile_id,
        utc_now
      )
    end)
    |> Repo.transaction(timeout: 900_000)
    |> case do
      {:ok, %{upsert_custom_contacts: {num_of_upserted_custom_contacts, contacts}}} ->
        updated_count =
          Enum.count(contacts, fn contact ->
            contact.inserted_at != utc_now
          end)

        {:ok, num_of_upserted_custom_contacts, updated_count}

      _ ->
        {:error, "Could not upsert a contact list."}
    end
  end

  def prep_contact_tags(multi, contacts_key, tags, company_profile_id, company_profile_user_id, utc_now) do
    if is_list(tags) and tags != [] do
      if FeatureFlags.enabled?(:static_lists, for: Gaia.Companies.get_profile(company_profile_id)) do
        prep_contact_tags_new(
          multi,
          contacts_key,
          tags,
          company_profile_id,
          company_profile_user_id,
          utc_now
        )
      else
        prep_contact_tags_old(multi, contacts_key, tags, company_profile_id, utc_now)
      end
    else
      multi
    end
  end

  defp prep_contact_tags_old(multi, contacts_key, tags, company_profile_id, utc_now) do
    multi
    |> Ecto.Multi.run(:prepare_tags, fn _repo, changes ->
      contacts = extract_contacts_from_changes(changes, contacts_key)

      if contacts == [] do
        {:ok, []}
      else
        new_tags =
          Enum.flat_map(contacts, fn %{id: contact_id} ->
            Enum.map(
              tags,
              &%{
                name: &1 |> String.downcase() |> String.trim(),
                company_profile_id: company_profile_id,
                contact_id: contact_id,
                inserted_at: utc_now,
                updated_at: utc_now
              }
            )
          end)

        {:ok, new_tags}
      end
    end)
    |> Ecto.Multi.run(:insert_tags, fn repo, %{prepare_tags: new_tags} ->
      Enum.each(Enum.chunk_every(new_tags, 1000), fn chunk ->
        repo.insert_all(
          Gaia.Contacts.Tag,
          chunk,
          on_conflict: {:replace_all_except, [:id, :inserted_at]},
          conflict_target: {:unsafe_fragment, ~s<("contact_id", "name") WHERE invalidated IS FALSE>}
        )
      end)

      {:ok, :done}
    end)
  end

  defp prep_contact_tags_new(multi, contacts_key, tags, company_profile_id, company_profile_user_id, utc_now) do
    multi
    |> Ecto.Multi.run(:existing_static_lists, fn repo, _changes ->
      existing_static_lists =
        StaticList
        |> where([s], s.company_profile_id == ^company_profile_id)
        |> where([s], s.name in ^tags)
        |> select([s], %{id: s.id, name: s.name})
        |> repo.all()

      {:ok, existing_static_lists}
    end)
    |> Ecto.Multi.run(:prepare_new_static_lists, fn _repo,
                                                    %{
                                                      existing_static_lists: existing_static_lists
                                                    } ->
      existing_tag_names = Enum.map(existing_static_lists, & &1.name)
      tags_to_create = tags -- existing_tag_names

      new_static_lists =
        Enum.map(tags_to_create, fn tag_name ->
          %{
            name: tag_name,
            company_profile_id: company_profile_id,
            background_color: "bg-gray-100",
            text_color: "text-gray-700",
            inserted_at: utc_now,
            updated_at: utc_now,
            last_updated_by_profile_user_id: company_profile_user_id
          }
        end)

      {:ok, new_static_lists}
    end)
    |> Ecto.Multi.insert_all(
      :insert_new_static_lists,
      StaticList,
      fn %{prepare_new_static_lists: new_static_lists} -> new_static_lists end,
      on_conflict: :nothing,
      conflict_target: {:unsafe_fragment, ~s<("company_profile_id", "name") WHERE invalidated IS NOT true>},
      returning: [:id, :name]
    )
    |> Ecto.Multi.run(:all_static_lists, fn _repo,
                                            %{
                                              existing_static_lists: existing_static_lists,
                                              insert_new_static_lists: {_, inserted_static_lists}
                                            } ->
      all_static_lists = existing_static_lists ++ inserted_static_lists
      {:ok, all_static_lists}
    end)
    |> Ecto.Multi.run(:static_list_map, fn _repo, %{all_static_lists: all_static_lists} ->
      static_list_map = Map.new(all_static_lists, &{&1.name, &1.id})
      {:ok, static_list_map}
    end)
    |> Ecto.Multi.run(:insert_static_list_members, fn repo, changes ->
      contacts = extract_contacts_from_changes(changes, contacts_key)
      static_list_map = changes.static_list_map

      new_members =
        for contact <- contacts,
            tag_name <- tags,
            static_list_id = Map.get(static_list_map, tag_name),
            do: %{
              static_list_id: static_list_id,
              contact_id: contact.id,
              inserted_at: utc_now,
              updated_at: utc_now
            }

      Enum.each(Enum.chunk_every(new_members, 1000), fn chunk ->
        repo.insert_all(StaticListMember, chunk,
          on_conflict: {:replace, [:updated_at]},
          conflict_target: [:static_list_id, :contact_id]
        )
      end)

      {:ok, :done}
    end)
  end

  defp extract_contacts_from_changes(changes, contacts_key) do
    case Map.get(changes, contacts_key) do
      {_, contacts} when is_list(contacts) -> contacts
      contacts when is_list(contacts) -> contacts
      _ -> []
    end
  end

  def clean_upsert_custom_contacts_string_input(input) when is_binary(input) do
    input
    |> String.trim()
    |> case do
      "" -> nil
      trimmed_input -> trimmed_input
    end
  end

  def clean_upsert_custom_contacts_string_input(_input), do: nil

  @doc """
  Subscribes contact to a specific scope

  Support two scenarios for now:
  1. If contact is not globally unsubscribed, remove the scope from comms_contact_unsubscribes
  2. If contact is globally unsubscribed, contact should only subscribed to that scope and no longer globally unsubscribed
  """
  def subscribe_contact(%Contact{} = contact, scope) do
    preloaded_contact = Repo.preload(contact, [:comms_unsubscribes, :global_unsubscribe])

    case preloaded_contact.global_unsubscribe do
      nil ->
        update_contact(preloaded_contact, %{
          comms_unsubscribes:
            preloaded_contact.comms_unsubscribes
            |> Enum.filter(&(&1.scope != scope))
            |> Enum.map(&Map.from_struct/1)
        })

      _exists ->
        update_contact(preloaded_contact, %{
          comms_unsubscribes:
            ContactUnsubscribe.get_scope_values()
            |> Kernel.--([scope])
            |> Enum.map(
              &%{
                company_profile_id: contact.company_profile_id,
                contact_id: contact.id,
                scope: &1
              }
            ),
          global_unsubscribe: nil
        })
    end
  end

  @doc """
  Subscribe contact globally

  Delete comms_contact_global_unsubscribes
  Also cleanup all comms_contact_unsubscribes for that contact
  """
  def subscribe_contact_globally(%Contact{} = contact) do
    contact
    |> Repo.preload([:comms_unsubscribes, :global_unsubscribe])
    |> case do
      %Contact{global_unsubscribe: nil} = preloaded_contact ->
        update_contact(preloaded_contact, %{comms_unsubscribes: []})

      %Contact{global_unsubscribe: %ContactGlobalUnsubscribe{}} = preloaded_contact ->
        # The global_unsubscribe already deleted
        update_contact(preloaded_contact, %{comms_unsubscribes: [], global_unsubscribe: nil})
    end
  end

  @doc """
  Unsubscribe contact from a specific scope

  Insert comms_contact_unsubscribes
  Replace old comms_contact_unsubscribes if already exists
  The new row might contain updated data
  """
  def unsubscribe_contact(%Contact{} = contact, scope, email_id) do
    preloaded_contact = Repo.preload(contact, [:comms_unsubscribes])

    update_contact(preloaded_contact, %{
      comms_unsubscribes: [
        %{
          company_profile_id: contact.company_profile_id,
          contact_id: contact.id,
          unsubscribed_from_email_id: email_id,
          scope: scope
        }
        | preloaded_contact.comms_unsubscribes
          |> Enum.filter(&(&1.scope != scope))
          |> Enum.map(&Map.from_struct/1)
      ]
    })
  end

  @doc """
  Unsubscribe contact globally including future scopes

  Insert comms_contact_global_unsubscribes
  Also cleanup all comms_contact_unsubscribes for that contact
  """
  def unsubscribe_contact_globally(%Contact{} = contact, email_id) do
    contact
    |> Repo.preload([:comms_unsubscribes, :global_unsubscribe])
    |> case do
      %Contact{global_unsubscribe: nil} = preloaded_contact ->
        update_contact(preloaded_contact, %{
          comms_unsubscribes: [],
          global_unsubscribe: %{
            company_profile_id: preloaded_contact.company_profile_id,
            contact_id: preloaded_contact.id,
            unsubscribed_from_email_id: email_id
          }
        })

      %Contact{global_unsubscribe: %ContactGlobalUnsubscribe{}} = preloaded_contact ->
        # The global_unsubscribe already exists
        update_contact(preloaded_contact, %{comms_unsubscribes: []})
    end
  end

  def get_unverified_company_contacts_emails(company_profile_id) do
    Contact
    |> where([c], c.company_profile_id == ^company_profile_id)
    |> where([c], is_nil(c.email_validity_checked_at))
    |> select([c], c.email)
    |> Repo.all()
  end

  @doc """
  Returns the list of contacts_email_logs.

  ## Examples

      iex> list_contacts_email_logs()
      [%EmailLog{}, ...]

  """
  def list_contacts_email_logs do
    Repo.all(EmailLog)
  end

  @doc """
  Gets a single email_log.

  Raises `Ecto.NoResultsError` if the Email log does not exist.

  ## Examples

      iex> get_email_log!(123)
      %EmailLog{}

      iex> get_email_log!(456)
      ** (Ecto.NoResultsError)

  """
  def get_email_log!(id), do: Repo.get!(EmailLog, id)

  @doc """
  Gets a single email log.

  Returns nil if the EmailLog does not exist.

  Raises `Ecto.MultipleResultsError` if more than one entry found.

  ## Examples

      iex> get_email_log_by(%{key: value})
      %EmailLog{}

      iex> get_email_log_by(%{key: value})
      nil

      iex> get_email_log_by(%{key: value})
      ** (Ecto.MultipleResultsError)

  """
  def get_email_log_by(attrs), do: Repo.get_by(EmailLog, attrs)

  @doc """
  Creates a email_log.

  ## Examples

      iex> create_email_log(%{field: value})
      {:ok, %EmailLog{}}

      iex> create_email_log(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_email_log(attrs \\ %{}) do
    %EmailLog{}
    |> EmailLog.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a email_log.

  ## Examples

      iex> update_email_log(email_log, %{field: new_value})
      {:ok, %EmailLog{}}

      iex> update_email_log(email_log, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_email_log(%EmailLog{} = email_log, attrs) do
    email_log
    |> EmailLog.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a email_log.

  ## Examples

      iex> delete_email_log(email_log)
      {:ok, %EmailLog{}}

      iex> delete_email_log(email_log)
      {:error, %Ecto.Changeset{}}

  """
  def delete_email_log(%EmailLog{} = email_log) do
    Repo.delete(email_log)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking email_log changes.

  ## Examples

      iex> change_email_log(email_log)
      %Ecto.Changeset{data: %EmailLog{}}

  """
  def change_email_log(%EmailLog{} = email_log, attrs \\ %{}) do
    EmailLog.changeset(email_log, attrs)
  end

  @doc """
  Returns the list of contacts_dynamic_lists.

  ## Examples

      iex> list_contacts_dynamic_lists()
      [%DynamicList{}, ...]

  """
  def list_contacts_dynamic_lists do
    Repo.all(DynamicList)
  end

  @doc """
  Returns a map of id to %DynamicList{}

  ## Examples

      iex> batch_get_dynamic_lists(%{company_profile_id: 1}, [[1, 2], [2, 3]])
      [%DynamicList{}, ...]

  """
  def batch_get_dynamic_lists(%{company_profile_id: company_profile_id}, list_of_dynamic_list_ids) do
    unique_dynamic_list_ids =
      list_of_dynamic_list_ids
      |> List.flatten()
      |> Enum.uniq()

    DynamicList
    |> where(company_profile_id: ^company_profile_id)
    |> where([dl], dl.id in ^unique_dynamic_list_ids)
    |> Repo.all(with_invalidated: true)
    |> Map.new(fn dl -> {dl.id, dl} end)
  end

  @doc """
  Gets a single dynamic_list.

  Raises `Ecto.NoResultsError` if the Dynamic list does not exist.

  ## Examples

      iex> get_dynamic_list!(123)
      %DynamicList{}

      iex> get_dynamic_list!(456)
      ** (Ecto.NoResultsError)

  """
  def get_dynamic_list!(id), do: Repo.get!(DynamicList, id)

  @doc """
  Gets a single dynamic_list.

  Returns nil if the DynamicList does not exist.

  Raises `Ecto.MultipleResultsError` if more than one entry found.

  ## Examples

      iex> get_dynamic_list_by(%{key: value})
      %DynamicList{}

      iex> get_dynamic_list_by(%{key: value})
      nil

      iex> get_dynamic_list_by(%{key: value})
      ** (Ecto.MultipleResultsError)

  """
  def get_dynamic_list_by(attrs), do: Repo.get_by(DynamicList, attrs)

  @doc """
  Creates a dynamic_list.

  ## Examples

      iex> create_dynamic_list(%{field: value})
      {:ok, %DynamicList{}}

      iex> create_dynamic_list(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_dynamic_list(attrs \\ %{}) do
    %DynamicList{}
    |> DynamicList.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a dynamic_list.

  ## Examples

      iex> update_dynamic_list(dynamic_list, %{field: new_value})
      {:ok, %DynamicList{}}

      iex> update_dynamic_list(dynamic_list, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_dynamic_list(%DynamicList{} = dynamic_list, attrs) do
    dynamic_list
    |> DynamicList.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a dynamic_list.

  ## Examples

      iex> delete_dynamic_list(dynamic_list)
      {:ok, %DynamicList{}}

      iex> delete_dynamic_list(dynamic_list)
      {:error, %Ecto.Changeset{}}

  """
  def delete_dynamic_list(%DynamicList{} = dynamic_list) do
    Repo.delete(dynamic_list)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking dynamic_list changes.

  ## Examples

      iex> change_dynamic_list(dynamic_list)
      %Ecto.Changeset{data: %DynamicList{}}

  """
  def change_dynamic_list(%DynamicList{} = dynamic_list, attrs \\ %{}) do
    DynamicList.changeset(dynamic_list, attrs)
  end

  @doc """
  Returns a dynamic_lists query for a specific company.

  Used name bindings for making it easier to compose query
  """
  def dynamic_lists_query(company_profile_id, options \\ %{}) do
    base_query =
      DynamicList
      |> from(as: :dynamic_list)
      |> where([dynamic_list: dl], dl.company_profile_id == ^company_profile_id)
      |> where([dynamic_list: dl], not dl.invalidated)

    Enum.reduce(options, base_query, fn
      {:filters, filters}, query_acc -> filter_dynamic_lists_query(query_acc, filters)
      {:orders, orders}, query_acc -> order_dynamic_lists_query(query_acc, orders)
    end)
  end

  defp filter_dynamic_lists_query(input_query, filters) do
    Enum.reduce(filters, input_query, fn
      %{key: _, value: ""}, query ->
        query

      %{key: "name", value: name}, query ->
        where(query, [q], ilike(q.name, ^"%#{name}%"))
    end)
  end

  defp order_dynamic_lists_query(input_query, orders) do
    Enum.reduce(orders, input_query, fn
      %{key: "id", value: "asc"}, query ->
        order_by(query, [q], asc: q.id)

      %{key: "id", value: "desc"}, query ->
        order_by(query, [q], desc: q.id)

      %{key: "last_updated_at", value: "asc"}, query ->
        order_by(query, [q], asc: q.last_updated_at)

      %{key: "last_updated_at", value: "desc"}, query ->
        order_by(query, [q], desc: q.last_updated_at)

      %{key: "name", value: "asc"}, query ->
        order_by(query, [q], asc: q.name)

      %{key: "name", value: "desc"}, query ->
        order_by(query, [q], desc: q.name)
    end)
  end

  @doc """
  Returns a list of contact ids who are part of the dynamic list.

  ## Examples

      iex> list_contact_ids_from_dynamic_list(%DynamicList{})
      [1, 2]

  """
  def list_contact_ids_from_dynamic_list(nil), do: []

  def list_contact_ids_from_dynamic_list(%DynamicList{company_profile_id: company_profile_id, filters: filters}) do
    company_profile_id
    |> contacts_query(%{filters: filters})
    |> select([contact: c], c.id)
    |> Repo.all()
  end

  @doc """
  Returns a list of unique contact ids who are part of the given dynamic lists.

  ## Examples

      iex> list_contact_ids_from_dynamic_list_ids(1, [1, 2])
      [1, 2]

  """
  def list_contact_ids_from_dynamic_list_ids(company_profile_id, dynamic_list_ids) when is_list(dynamic_list_ids) do
    dynamic_list_ids
    |> Enum.reduce([], fn dynamic_list_id, acc ->
      %{company_profile_id: company_profile_id, id: dynamic_list_id}
      |> get_dynamic_list_by()
      |> list_contact_ids_from_dynamic_list()
      |> Kernel.++(acc)
    end)
    |> Enum.uniq()
  end

  @doc """
  Returns the number of contacts inside the dynamic list.

  ## Examples

      iex> calculate_dynamic_list_contacts_size(%DynamicList{})
      1_000

  """
  def calculate_dynamic_list_contacts_size(nil), do: 0

  def calculate_dynamic_list_contacts_size(%DynamicList{company_profile_id: company_profile_id, filters: filters}) do
    company_profile_id
    |> contacts_query(%{filters: filters})
    |> Repo.aggregate(:count)
  end

  @doc """
  Returns true if dynamic list name already exists.

  ## Examples

      iex> is_dynamic_list_name_taken?(1, "Name")
      true

  """
  def is_dynamic_list_name_taken?(company_profile_id, name) do
    DynamicList
    |> where(company_profile_id: ^company_profile_id)
    |> where(name: ^name)
    |> Repo.exists?()
  end

  @doc """
  Insert recommended dynamic lists if they are not already present.

  check the presence by the name of the dynamic list.

  ## Examples

      iex> insert_recommended_dynamic_lists(%Profile{})
      {:ok, 3}

  """
  def insert_recommended_dynamic_lists(%Profile{id: company_profile_id} = profile) do
    utc_now = Helper.ExDay.utc_now()

    attrs =
      profile
      |> recommended_dynamic_lists_attrs()
      |> Enum.reject(fn %{name: name} -> is_dynamic_list_name_taken?(profile.id, name) end)
      |> Enum.map(fn list_attrs ->
        list_attrs
        |> Map.merge(%{
          company_profile_id: company_profile_id,
          last_updated_at: utc_now,
          inserted_at: utc_now,
          updated_at: utc_now
        })
        |> Map.update(:filters, [], fn filters ->
          Enum.map(filters, &struct(DynamicList.Filters, &1))
        end)
      end)

    {inserted_rows_count, dynamic_lists} =
      Repo.insert_all(DynamicList, attrs, on_conflict: :nothing, returning: true)

    # Enqueue jobs to calculate estimated_contacts_size
    Gaia.Workers.CalculateDynamicListSizes.enqueue_job(dynamic_lists)

    {:ok, inserted_rows_count}
  end

  @doc """
  Returns the recommended dynamic lists attributes.

  ## Examples

      iex> recommended_dynamic_lists_attrs(%Profile{})
      [%{}]

  """
  def recommended_dynamic_lists_attrs(%Profile{is_premium: false}) do
    [
      %{
        description:
          "All contacts that have been identified as a potential shareholder either by themselves or someone within your company.",
        filters: [%{key: "shareholder_status", value: "nominated-shareholder"}],
        name: "All nominated shareholders"
      },
      %{
        description: "All investor who have signed up to your hub.",
        filters: [%{key: "has_investor_hub_user", value: "linked-only"}],
        name: "All hub contacts"
      }
    ]
  end

  def recommended_dynamic_lists_attrs(%Profile{is_premium: true}) do
    [
      %{
        description: "All current shareholders in your registry that have provided an email address.",
        filters: [%{key: "shareholder_status", value: "shareholder"}],
        name: "All shareholders"
      },
      %{
        description: "All investor who have signed up to your hub.",
        filters: [%{key: "has_investor_hub_user", value: "linked-only"}],
        name: "All hub contacts"
      },
      %{
        description: "Contacts who upgraded their holding position in the last 30 days",
        filters: [%{key: "trading_activity", value: "upgrader,30"}],
        name: "Upgraded in the last 30 days"
      },
      %{
        description: "Contacts who downgraded their holding position in the last 30 days",
        filters: [%{key: "trading_activity", value: "downgrader,30"}],
        name: "Downgraded in the last 30 days"
      },
      %{
        description: "Contacts who sold down all of their holdings in the last 30 days",
        filters: [%{key: "trading_activity", value: "churned,30"}],
        name: "Churned in the last 30 days"
      },
      %{
        description: "Contacts who were past shareholders and bought shares in the last 30 days",
        filters: [%{key: "trading_activity", value: "returning,30"}],
        name: "Returned in the last 30 days"
      }
    ]
  end

  @doc """
  Returns the list of contacts_static_lists.

  ## Examples

      iex> list_contacts_static_lists()
      [%StaticList{}, ...]

  """
  def list_contacts_static_lists do
    Repo.all(StaticList)
  end

  @doc """
  Gets a single static_list.

  Raises `Ecto.NoResultsError` if the Static List does not exist.

  ## Examples

      iex> get_static_list!(123)
      %StaticList{}

      iex> get_static_list!(456)
      ** (Ecto.NoResultsError)

  """
  def get_static_list!(id), do: Repo.get!(StaticList, id)

  @doc """
  Gets a single static_list.

  ## Examples

      iex> get_static_list(123)
      %StaticList{}

      iex> get_static_list(456)
      nil

      iex> get_static_list(nil)
      nil
  """

  def get_static_list(id) when not is_nil(id) do
    Repo.get(StaticList, id)
  end

  def get_static_list(nil), do: nil

  @doc """
  Gets a single static_list.

  Returns nil if the StaticList does not exist.

  Raises `Ecto.MultipleResultsError` if more than one entry found.

  ## Examples

      iex> get_static_list_by(%{key: value})
      %StaticList{}

      iex> get_static_list_by(%{key: value})
      nil

      iex> get_static_list_by(%{key: value})
      ** (Ecto.MultipleResultsError)

  """
  def get_static_list_by(attrs), do: Repo.get_by(StaticList, attrs)

  @doc """
  Creates a static_list.

  ## Examples

      iex> create_static_list(%{field: value})
      {:ok, %StaticList{}}

      iex> create_static_list(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_static_list(attrs \\ %{}) do
    %StaticList{}
    |> StaticList.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Gets a static list by name and company profile id, or creates a new one if it doesn't exist.

  ## Examples

      iex> get_or_create_static_list("My List", 1)
      {:ok, %StaticList{}}

  """
  def get_or_create_static_list(name, company_profile_id, attrs \\ %{}) do
    %{name: name, company_profile_id: company_profile_id}
    |> get_static_list_by()
    |> case do
      {:ok, %StaticList{} = static_list} ->
        {:ok, static_list}

      _ ->
        attrs
        |> Map.put(:name, name)
        |> Map.put(:company_profile_id, company_profile_id)
        |> create_static_list()
    end
  end

  @doc """
  Updates a static_list.

  ## Examples

      iex> update_static_list(static_list, %{field: new_value})
      {:ok, %StaticList{}}

      iex> update_static_list(static_list, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_static_list(%StaticList{} = static_list, attrs) do
    static_list
    |> StaticList.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a static_list.

  ## Examples

      iex> delete_static_list(static_list)
      {:ok, %StaticList{}}

      iex> delete_static_list(static_list)
      {:error, %Ecto.Changeset{}}

  """
  def delete_static_list(%StaticList{} = static_list) do
    Repo.delete(static_list)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking static_list changes.

  ## Examples

      iex> change_static_list(static_list)
      %Ecto.Changeset{data: %StaticList{}}

  """
  def change_static_list(%StaticList{} = static_list, attrs \\ %{}) do
    StaticList.changeset(static_list, attrs)
  end

  @doc """
  Returns the list of contacts_static_list_members.

  ## Examples

      iex> list_contacts_static_list_members()
      [%StaticListMember{}, ...]

  """
  def list_contacts_static_list_members do
    Repo.all(StaticListMember)
  end

  @doc """
  Gets a single static_list_member.

  Raises `Ecto.NoResultsError` if the Static List does not exist.

  ## Examples

      iex> get_static_list_member!(123)
      %StaticListMember{}

      iex> get_static_list_member!(456)
      ** (Ecto.NoResultsError)

  """
  def get_static_list_member!(id), do: Repo.get!(StaticListMember, id)

  @doc """
  Gets a single static_list_member.

  ## Examples

      iex> get_static_list_member(123)
      %StaticListMember{}

      iex> get_static_list_member(456)
      nil
  """

  def get_static_list_member(id) do
    Repo.get(StaticListMember, id)
  end

  @doc """
  Gets a single static_list_member.

  Returns nil if the StaticListMember does not exist.

  Raises `Ecto.MultipleResultsError` if more than one entry found.

  ## Examples

      iex> get_static_list_member_by(%{key: value})
      %StaticListMember{}

      iex> get_static_list_member_by(%{key: value})
      nil

      iex> get_static_list_member_by(%{key: value})
      ** (Ecto.MultipleResultsError)
  """

  def get_static_list_member_by(attrs), do: Repo.get_by(StaticListMember, attrs)

  def if_contact_have_tag?(%Contact{} = contact, name) do
    contact
    |> Repo.preload(:static_lists)
    |> case do
      %Contact{static_lists: nil} ->
        false

      %Contact{static_lists: [%StaticList{} | _] = static_lists} ->
        Enum.any?(static_lists, &(&1.name == name))

      _ ->
        false
    end
  end

  @doc """
  Creates a static_list_member.

  ## Examples

      iex> create_static_list_member(%{field: value})
      {:ok, %StaticListMember{}}

      iex> create_static_list_member(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """

  def create_static_list_member(attrs \\ %{}) do
    %StaticListMember{}
    |> StaticListMember.changeset(attrs)
    |> Repo.insert()
  end

  def maybe_create_static_list_member(attrs \\ %{}) do
    %StaticListMember{}
    |> StaticListMember.changeset(attrs)
    |> Repo.insert(
      on_conflict: :nothing,
      conflict_target: [:static_list_id, :contact_id],
      returning: true
    )
  end

  @doc """
  Updates a static_list_member.

  ## Examples

      iex> update_static_list_member(static_list_member, %{field: new_value})
      {:ok, %StaticListMember{}}

      iex> update_static_list_member(static_list_member, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_static_list_member(%StaticListMember{} = static_list_member, attrs) do
    static_list_member
    |> StaticListMember.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a static_list_member.

  ## Examples

      iex> delete_static_list_member(static_list_member)
      {:ok, %StaticListMember{}}

      iex> delete_static_list_member(static_list_member)
      {:error, %Ecto.Changeset{}}

  """
  def delete_static_list_member(%StaticListMember{} = static_list_member) do
    Repo.delete(static_list_member)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking static_list_member changes.

  ## Examples

      iex> change_static_list_member(static_list_member)
      %Ecto.Changeset{data: %StaticListMember{}}

  """
  def change_static_list_member(%StaticListMember{} = static_list_member, attrs \\ %{}) do
    StaticListMember.changeset(static_list_member, attrs)
  end

  @doc """
  Adds new contacts to a static list and removes existings ones that are no longer included.

  ## Examples

      iex> sync_static_list_members(%StaticList{id: 1}, [1, 2, 3])
      {:ok, _}

  """
  def sync_static_list_members(%StaticList{id: static_list_id}, contact_ids) when is_list(contact_ids) do
    batch_size = 1000

    Repo.transaction(fn ->
      existing_members =
        Repo.all(
          from(slm in StaticListMember,
            where: slm.static_list_id == ^static_list_id,
            select: slm.contact_id
          )
        )

      contact_ids =
        Enum.map(contact_ids, fn contact_id ->
          if is_binary(contact_id) do
            String.to_integer(contact_id)
          else
            contact_id
          end
        end)

      # Those in DB but not sent by frontend
      members_to_remove =
        Enum.reject(existing_members, fn contact_id ->
          Enum.member?(contact_ids, contact_id)
        end)

      members_to_insert =
        Enum.reject(contact_ids, fn contact_id ->
          Enum.member?(existing_members, contact_id)
        end)

      if members_to_remove != [] do
        Repo.delete_all(
          from(slm in StaticListMember,
            where: slm.static_list_id == ^static_list_id and slm.contact_id in ^members_to_remove
          )
        )
      end

      contact_batches = Enum.chunk_every(members_to_insert, batch_size)

      Enum.each(contact_batches, fn contact_batch ->
        members =
          Enum.map(contact_batch, fn contact_id ->
            %{
              static_list_id: static_list_id,
              contact_id: contact_id,
              inserted_at: NaiveDateTime.utc_now(:second),
              updated_at: NaiveDateTime.utc_now(:second)
            }
          end)

        # Insert all batch members, skipping already existing ones due to the unique constraint
        Repo.insert_all(StaticListMember, members, on_conflict: :nothing)
      end)
    end)
  end

  @doc """
  Returns true if static list name already exists.

  ## Examples

      iex> is_static_list_name_taken?(1, "Name")
      true

  """
  def is_static_list_name_taken?(company_profile_id, name) do
    StaticList
    |> where(company_profile_id: ^company_profile_id)
    |> where(name: ^name)
    |> Repo.exists?()
  end

  @doc """
  Returns a static_lists query for a specific company.
  """
  def static_lists_query(company_profile_id, options \\ %{}) do
    base_query =
      StaticList
      |> from(as: :static_list)
      |> where([static_list: sl], sl.company_profile_id == ^company_profile_id)
      |> where([static_list: sl], not sl.invalidated)

    Enum.reduce(options, base_query, fn
      {:filters, filters}, query_acc -> filter_static_lists_query(query_acc, filters)
      {:orders, orders}, query_acc -> order_static_lists_query(query_acc, orders)
    end)
  end

  defp filter_static_lists_query(input_query, filters) do
    Enum.reduce(filters, input_query, fn
      %{key: _, value: ""}, query ->
        query

      %{key: "name", value: name}, query ->
        where(query, [q], ilike(q.name, ^"%#{name}%"))
    end)
  end

  defp order_static_lists_query(input_query, orders) do
    Enum.reduce(orders, input_query, fn
      %{key: "id", value: "asc"}, query ->
        order_by(query, [q], asc: q.id)

      %{key: "id", value: "desc"}, query ->
        order_by(query, [q], desc: q.id)

      %{key: "last_updated_at", value: "asc"}, query ->
        order_by(query, [q], asc: q.updated_at)

      %{key: "last_updated_at", value: "desc"}, query ->
        order_by(query, [q], desc: q.updated_at)

      %{key: "name", value: "asc"}, query ->
        order_by(query, [q], asc: q.name)

      %{key: "name", value: "desc"}, query ->
        order_by(query, [q], desc: q.name)
    end)
  end

  def get_static_list_suggested_names(company_profile_id) do
    default_suggested = ["broker", "institutional", "retail", "media", "met at roadshow"]

    existing_tags =
      StaticList
      |> where(company_profile_id: ^company_profile_id)
      |> where([sl], fragment("lower(?)", sl.name) in ^default_suggested)
      |> select([sl], fragment("lower(?)", sl.name))
      |> Repo.all()

    default_suggested |> Enum.reject(&(&1 in existing_tags)) |> Enum.map(&String.capitalize/1)
  end

  def batch_get_static_list_member_contact_ids(%{company_profile_id: company_profile_id}, static_list_ids) do
    StaticListMember
    |> join(:inner, [slm], sl in assoc(slm, :static_list))
    |> where([slm, sl], sl.company_profile_id == ^company_profile_id)
    |> where([slm, sl], not sl.invalidated)
    |> where([slm], slm.static_list_id in ^static_list_ids)
    |> group_by([slm], slm.static_list_id)
    |> select([slm], %{
      static_list_id: slm.static_list_id,
      contact_ids: fragment("array_agg(?)", slm.contact_id)
    })
    |> Repo.all()
  end

  def get_static_list_member_contact_ids_from_static_list_ids(company_profile_id, static_list_ids) do
    StaticListMember
    |> join(:inner, [slm], sl in assoc(slm, :static_list))
    |> where([slm, _sl], slm.static_list_id in ^static_list_ids)
    |> where([_slm, sl], sl.company_profile_id == ^company_profile_id)
    |> select([slm, _sl], slm.contact_id)
    |> Repo.all()
    |> Enum.uniq()
  end

  def get_static_list_member_count(static_list_id) do
    StaticListMember
    |> join(:inner, [slm], sl in assoc(slm, :static_list))
    |> where([slm, sl], sl.id == ^static_list_id)
    |> select([slm, sl], count(slm.contact_id))
    |> Repo.one()
  end

  def batch_get_static_list_total_members(%{company_profile_id: company_profile_id}, static_list_ids) do
    StaticListMember
    |> join(:inner, [slm], sl in assoc(slm, :static_list))
    |> where([slm, sl], sl.company_profile_id == ^company_profile_id)
    |> where([slm, sl], not sl.invalidated)
    |> where([slm, sl], slm.static_list_id in ^static_list_ids)
    |> group_by([slm, sl], slm.static_list_id)
    |> select([slm, sl], %{
      static_list_id: slm.static_list_id,
      total_members: count(slm.contact_id)
    })
    |> Repo.all()
  end

  def batch_get_static_contactable_total_members(%{company_profile_id: company_profile_id}, static_list_ids) do
    StaticListMember
    |> join(:inner, [slm], sl in assoc(slm, :static_list))
    |> join(:inner, [slm, sl], c in assoc(slm, :contact))
    |> where([slm, sl], sl.company_profile_id == ^company_profile_id)
    |> where([slm, sl], not sl.invalidated)
    |> where([slm, sl], slm.static_list_id in ^static_list_ids)
    |> where([slm, sl, c], not is_nil(c.email))
    |> where([_slm, _sl, c], c.email_validity_result == ^:valid)
    |> group_by([slm, sl], slm.static_list_id)
    |> select([slm, sl], %{
      static_list_id: slm.static_list_id,
      total_members: count(slm.contact_id)
    })
    |> Repo.all()
  end

  @doc """
  Returns a map of id to %StaticList{}

  ## Examples

      iex> batch_get_static_lists(%{company_profile_id: 1}, [[1, 2], [2, 3]])
      [%StaticList{}, ...]

  """
  def batch_get_static_lists(%{company_profile_id: company_profile_id}, list_of_static_list_ids) do
    unique_static_list_ids =
      list_of_static_list_ids
      |> List.flatten()
      |> Enum.uniq()

    StaticList
    |> where(company_profile_id: ^company_profile_id)
    |> where([sl], sl.id in ^unique_static_list_ids)
    |> Repo.all(with_invalidated: true)
    |> Map.new(fn sl -> {sl.id, sl} end)
  end

  def invalidate_static_list(%StaticList{} = static_list, attr) do
    attr = Map.put(attr, :invalidated, true)
    update_static_list(static_list, attr)

    StaticListMember
    |> where([slm], slm.static_list_id == ^static_list.id)
    |> Repo.delete_all()

    {:ok, nil}
  end

  @doc """
  Creates a static list with or without contacts.

  ## Examples
      iex> create_static_list_with_contacts(%StaticList{}, [1, 2, 3])
      {:ok, %StaticList{}}
  """

  def create_static_list_with_contacts(static_list_input, contact_ids) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:static_list, fn _repo, _changes ->
      create_static_list(static_list_input)
    end)
    |> Ecto.Multi.run(:static_list_members, fn _repo, %{static_list: static_list} ->
      if contact_ids != [] do
        sync_static_list_members(static_list, contact_ids)
      else
        {:ok, []}
      end
    end)
    |> Repo.transaction()
    |> case do
      {:ok, res} -> {:ok, res}
      {:error, res} -> {:error, res}
      {:error, _, res, _} -> {:error, res}
    end
    |> handle_transaction_result(:static_list)
  end

  @doc """

  Updates a static list with or without contacts.

  ## Examples

      iex> update_static_list_with_contacts(%StaticList{}, %{}, [1, 2, 3])
      {:ok, %StaticList{}}
  """

  def update_static_list_with_contacts(static_list, attrs, contact_ids) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:static_list, fn _repo, _changes ->
      update_static_list(static_list, attrs)
    end)
    |> Ecto.Multi.run(:static_list_members, fn _repo, %{static_list: static_list} ->
      if contact_ids != [] do
        sync_static_list_members(static_list, contact_ids)
      else
        {:ok, []}
      end
    end)
    |> Repo.transaction()
    |> case do
      {:ok, res} -> {:ok, res}
      {:error, res} -> {:error, res}
      {:error, _, res, _} -> {:error, res}
    end
    |> handle_transaction_result(:static_list)
  end

  @doc """
  Returns a list of map.

  It includes static lists created for the company profile.

  ## Examples

    iex> existing_static_lists_by_company_profile(profile)
    [%{id: "id", name: "name"}...]

  """
  def existing_static_lists_by_company_profile(%Profile{id: company_profile_id}),
    do: existing_static_lists_by_company_profile(company_profile_id)

  def existing_static_lists_by_company_profile(company_profile_id) do
    StaticList
    |> select([sl], sl)
    |> where([sl], company_profile_id: ^company_profile_id)
    |> where([sl], not sl.invalidated)
    |> order_by([sl], asc: sl.name)
    |> Repo.all()
  end

  defp handle_transaction_result({:ok, map}, key) do
    {:ok, Map.get(map, key)}
  end

  defp handle_transaction_result({:error, errors}, _key) do
    {:error, errors}
  end

  def bulk_unsubscribe_contacts(
        contacts,
        true = _is_global_unsubscribe,
        _unsubscribe_scopes,
        apply_subscription_to_new_contact_only,
        company_profile_id,
        utc_now
      ) do
    contact_ids =
      contacts
      |> get_bulk_unsubscribe_contacts(apply_subscription_to_new_contact_only, utc_now)
      |> Enum.map(& &1.id)

    deleted_comms_contact_unsubscribes_query =
      where(
        ContactUnsubscribe,
        [cu],
        cu.company_profile_id == ^company_profile_id and cu.contact_id in ^contact_ids
      )

    Ecto.Multi.new()
    |> Ecto.Multi.run(:bulk_global_unsubscribe, fn repo, _ ->
      contact_ids
      |> Enum.chunk_every(1000)
      |> Enum.each(fn chunk ->
        repo.insert_all(
          ContactGlobalUnsubscribe,
          Enum.map(
            chunk,
            &%{
              company_profile_id: company_profile_id,
              contact_id: &1,
              inserted_at: utc_now,
              updated_at: utc_now
            }
          ),
          on_conflict: :nothing
        )
      end)

      {:ok, :done}
    end)
    |> Ecto.Multi.delete_all(
      :delete_comms_contact_unsubscribes,
      deleted_comms_contact_unsubscribes_query
    )
    |> Repo.transaction(timeout: 900_000)
  end

  # Bulk unsubscribe contacts who are not unsubscribe_globally
  def bulk_unsubscribe_contacts(
        contacts,
        false = _is_global_unsubscribe,
        unsubscribe_scopes,
        apply_subscription_to_new_contact_only,
        company_profile_id,
        utc_now
      ) do
    non_global_unsubscribed_contacts =
      contacts
      |> get_bulk_unsubscribe_contacts(apply_subscription_to_new_contact_only, utc_now)
      |> Repo.preload([:comms_unsubscribes, :global_unsubscribe])
      |> Enum.filter(&is_nil(&1.global_unsubscribe))

    hub_related_scopes = [:activity_follow, :qa, :raises, :new_follower]

    unsubscribe_scopes =
      Enum.map(unsubscribe_scopes, &(&1 |> String.downcase() |> String.to_atom()))

    Ecto.Multi.new()
    |> Ecto.Multi.run(:bulk_upsert_comms_contact_unsubscribes, fn repo, _changes ->
      comms_unsubscribes =
        Enum.flat_map(non_global_unsubscribed_contacts, fn contact ->
          contact
          |> get_unsubscribe_scopes(
            unsubscribe_scopes,
            hub_related_scopes,
            contact.inserted_at == utc_now
          )
          |> Enum.map(
            &%{
              company_profile_id: company_profile_id,
              contact_id: contact.id,
              scope: &1,
              inserted_at: utc_now,
              updated_at: utc_now
            }
          )
        end)

      comms_unsubscribes
      |> Enum.chunk_every(1000)
      |> Enum.each(fn chunk ->
        repo.insert_all(ContactUnsubscribe, chunk, on_conflict: :nothing)
      end)

      {:ok, :done}
    end)
    |> Repo.transaction(timeout: 900_000)
  end

  # Apply email subscription settings to New contacts only (excludes existing contacts)
  def get_bulk_unsubscribe_contacts(contacts, true = _apply_subscription_to_new_contact_only, utc_now) do
    Enum.filter(contacts, &(&1.inserted_at === utc_now))
  end

  # Apply email subscription settings to all imported contacts
  def get_bulk_unsubscribe_contacts(contacts, _apply_subscription_to_new_contact_only, _utc_now), do: contacts

  # If contact is non hub member, the default settings of all hub-related subscriptions are off
  defp get_unsubscribe_scopes(%Contact{} = _contact, scopes, hub_related_scopes, true = _is_new_contact) do
    scopes ++ hub_related_scopes
  end

  # If contact is hub member, the default settings of hub-related subscriptions are same as current
  defp get_unsubscribe_scopes(
         %Contact{comms_unsubscribes: _comms_unsubscribes} = _contact,
         scopes,
         _hub_related_scopes,
         false = _is_new_contact
       ) do
    scopes
  end

  def update_dynamic_lists_filters_with_tags do
    # Expected result %{company_profile_id: %{static_list_name: static_list_value}}
    tag_to_static_list_id_map =
      StaticList
      |> select([sl], {sl.company_profile_id, sl.name, sl.id})
      |> Repo.all()
      |> Enum.reduce(%{}, fn {outer_key, inner_key, value}, acc ->
        Map.update(acc, outer_key, %{inner_key => value}, fn inner_map ->
          Map.put(inner_map, inner_key, value)
        end)
      end)

    result =
      DynamicList
      |> where([dl], ilike(type(dl.filters, :string), "%tags%"))
      |> Repo.all()
      |> Enum.map(fn dl ->
        updated_filters =
          Enum.map(dl.filters, fn
            %{id: filter_item_id, key: "tags", value: tags_value} = existing_filter_item ->
              static_list_ids_value =
                tags_value
                |> String.split(",")
                |> Enum.map_join(",", fn tag_name ->
                  tag_to_static_list_id_map
                  |> Map.get(dl.company_profile_id)
                  |> Map.get(tag_name |> String.trim() |> String.downcase())
                end)

              new_filter_item = %{
                id: filter_item_id,
                key: "static_list_ids",
                value: static_list_ids_value
              }

              # Validate that both filters return the same number of contacts
              existing_filter_result_count =
                dl.company_profile_id
                |> contacts_query(%{filters: [existing_filter_item]})
                |> Repo.aggregate(:count)

              new_filter_result_count =
                dl.company_profile_id
                |> contacts_query(%{filters: [new_filter_item]})
                |> Repo.aggregate(:count)

              true = existing_filter_result_count == new_filter_result_count

              # Returns
              new_filter_item

            filter_item ->
              # Do not modify if not filtering tags
              Map.from_struct(filter_item)
          end)

        {:ok, _} = Gaia.Contacts.update_dynamic_list(dl, %{filters: updated_filters})
      end)

    # Validate that the loop is successful
    loop_count = Enum.count(result)

    success_count =
      Enum.count(result, fn
        {:ok, _} -> true
        _ -> false
      end)

    true = loop_count == success_count

    {:ok, result}
  end

  # RANK CONTACT BASED ON THEIR TOTAL SHAREHOLDINGS
  # Step 1: Build the aggregation query.
  def total_share_count_per_contact(company_profile_id) do
    from(c in Contact,
      where: c.company_profile_id == ^company_profile_id,
      join: sh in assoc(c, :shareholdings),
      group_by: c.id,
      select: %{
        id: c.id,
        total_share_count: sum(sh.share_count)
      }
    )
  end

  # Step 2: Create a subquery used to query for the contact's rank.
  def rank_contacts_by_total_shareholdings(company_profile_id) do
    total_query = total_share_count_per_contact(company_profile_id)

    from(t in subquery(total_query),
      select: %{
        id: t.id,
        rank:
          fragment(
            "RANK() OVER (ORDER BY ? DESC)",
            t.total_share_count
          )
      }
    )
  end

  # Step 3: Query the ranking for a specific contact.
  def get_contact_total_shareholding_rank(contact_id, company_profile_id) do
    ranking_query = rank_contacts_by_total_shareholdings(company_profile_id)

    query =
      from(r in subquery(ranking_query),
        where: r.id == ^contact_id,
        select: r.rank
      )

    Repo.one(query)
  end

  def filter_duplicate_email_actions_within_period(query, period \\ 10)

  def filter_duplicate_email_actions_within_period(query, period) do
    with_lag =
      query
      |> subquery(as: :q)
      |> select(
        [q],
        %{
          email_id: q.email_id,
          contact_id: q.contact_id,
          event_type: q.event_type,
          event_time: q.event_time,
          prev_event_time:
            fragment(
              "LAG(?) OVER (PARTITION BY ?, ? ORDER BY ?)",
              q.event_time,
              q.contact_id,
              q.email_id,
              q.event_time
            )
        }
      )

    with_lag
    |> subquery(as: :wl)
    |> where(
      [wl],
      fragment("extract(epoch from (? - ?)) > ?", wl.event_time, wl.prev_event_time, ^period)
    )
    |> select(
      [wl],
      %{
        email_id: wl.email_id,
        contact_id: wl.contact_id,
        event_type: wl.event_type,
        event_time: wl.event_time
      }
    )
  end

  @doc """
  Calculate the email engagement score per contact.
  TODO: Make this over all contacts in the company profile
  """
  def calculate_email_engagement_score_per_contact(company_profile_id, weights \\ %{open: 1.0, click: 1.0})

  def calculate_email_engagement_score_per_contact(company_profile_id, %{open: open_weight, click: click_weight}) do
    # Step 1: Get all email actions for contact
    actions =
      Gaia.Tracking.EmailEvent
      |> join(:inner, [ee], cer in Gaia.Comms.EmailRecipient, on: cer.tracking_email_id == ee.email_id)
      |> join(:inner, [ee, cer], e in Gaia.Comms.Email, on: cer.email_id == e.id)
      |> where([ee, cer, e], ee.event_type in [:Delivery, :Open, :Click])
      |> select([ee, cer], %{
        email_id: cer.email_id,
        contact_id: cer.contact_id,
        event_type: type(ee.event_type, :string),
        event_time: ee.inserted_at
      })

    delivery = where(actions, [a], a.event_type == ^:Delivery)
    # Step 1a: Remove duplicate open/click within 10s
    opens =
      actions
      |> where([a], a.event_type == ^:Open)
      |> filter_duplicate_email_actions_within_period()

    clicks =
      actions
      |> where([a], a.event_type == ^:Click)
      |> filter_duplicate_email_actions_within_period()

    # Step 2: Get all unopened/clicked emails
    # full join deliver and opens/clicks and find the difference
    unopened_emails =
      from(d in subquery(delivery),
        left_join: o in subquery(opens),
        on: d.email_id == o.email_id and d.contact_id == o.contact_id,
        where: is_nil(o.email_id),
        select: %{email_id: d.email_id, event_time: d.event_time, contact_id: d.contact_id}
      )

    unclicked_emails =
      from(d in subquery(delivery),
        left_join: c in subquery(clicks),
        on: d.email_id == c.email_id and d.contact_id == c.contact_id,
        where: is_nil(c.email_id),
        select: %{email_id: d.email_id, event_time: d.event_time, contact_id: d.contact_id}
      )

    # Step 3: Calculate the open/click rates
    rates = Gaia.Tracking.open_click_rates_by_company_profile(company_profile_id)

    # Step 4: Join all the rates and click/unclicked together and calculate the score
    # Score is a-r where a is action/inaction (1/0) and r is the rate
    opened_score =
      opens
      |> subquery()
      |> join(:inner, [o], r in subquery(rates), on: o.email_id == r.email_id)
      |> select([o, r], %{
        email_id: o.email_id,
        contact_id: o.contact_id,
        event_type: type(o.event_type, :string),
        event_time: o.event_time,
        score: 1 - r.open_rate
      })

    clicked_score =
      clicks
      |> subquery()
      |> join(:inner, [c], r in subquery(rates), on: c.email_id == r.email_id)
      |> select([c, r], %{
        email_id: c.email_id,
        contact_id: c.contact_id,
        event_type: type(c.event_type, :string),
        event_time: c.event_time,
        score: 1 - r.click_rate
      })

    unopened_score =
      unopened_emails
      |> subquery()
      |> join(:inner, [u], r in subquery(rates), on: u.email_id == r.email_id)
      |> select([u, r], %{
        email_id: u.email_id,
        contact_id: u.contact_id,
        event_type: type(^"Open", :string),
        event_time: u.event_time,
        score: -1.0 * r.open_rate
      })

    unclicked_score =
      unclicked_emails
      |> subquery()
      |> join(:inner, [u], r in subquery(rates), on: u.email_id == r.email_id)
      |> select([u, r], %{
        email_id: u.email_id,
        contact_id: u.contact_id,
        event_type: type(^"Click", :string),
        event_time: u.event_time,
        score: -1.0 * r.click_rate
      })

    # Union all the scores
    all_scores =
      opened_score
      |> union_all(^clicked_score)
      |> union_all(^unclicked_score)
      |> union_all(^unopened_score)

    # TODO - Need to assert number of emails opened/clicked/unopened/clicked makes sense
    # Step 5: Add time weighting to the scores
    # Calculate now - event_time in days
    # For every 30 days reduce by 0.1 until == 0
    time_weighted =
      all_scores
      |> subquery()
      |> select([as], %{
        email_id: as.email_id,
        contact_id: as.contact_id,
        event_type: as.event_type,
        event_time: as.event_time,
        score: as.score,
        time_diff: fragment("date_part('day', now() - ?)", as.event_time),
        time_weight:
          fragment(
            "case when date_part('day', now() - ?) < 360 then 1-floor(date_part('day', now() - ?)/30)*0.1 else 0.0 end",
            as.event_time,
            as.event_time
          )
      })

    deliveries_per_contact =
      delivery
      |> subquery()
      |> group_by([d], d.contact_id)
      |> select([d], %{contact_id: d.contact_id, deliveries: count(d.email_id, :distinct)})

    # Step 5: Sum all scores, weight open to close, to get the total score
    total_score_per_contact =
      time_weighted
      |> subquery()
      |> group_by([ts], [ts.contact_id, ts.event_type])
      |> select([ts], %{
        contact_id: ts.contact_id,
        interaction_weight:
          fragment(
            "case when ? = 'Click' then ? when ? = 'Open' then ? else 0.0 end",
            ts.event_type,
            ^click_weight,
            ts.event_type,
            ^open_weight
          ),
        time_weighted_score: sum(ts.score * ts.time_weight)
      })
      |> subquery()
      |> group_by([tsa], tsa.contact_id)
      |> select([tsa], %{
        contact_id: tsa.contact_id,
        total_score: sum(tsa.time_weighted_score * tsa.interaction_weight)
      })

    # Step 6: Average by the number of emails sent
    total_email_score_per_contact =
      total_score_per_contact
      |> subquery()
      |> join(:inner, [ts], d in subquery(deliveries_per_contact), on: ts.contact_id == d.contact_id)
      |> select([ts, d], %{
        contact_id: ts.contact_id,
        total_score: fragment("coalesce(? / nullif(?, 0), 0)", ts.total_score, d.deliveries)
      })
      |> Repo.all(timeout: 600_000)

    total_email_score_per_contact
  end

  # SHAREHOLDINGS TO CONTACTS MIGRATION
  def migrate_shareholdings_to_contacts do
    company_ids = Enum.map(Gaia.Companies.list_companies_profiles(), & &1.id)

    Enum.each(company_ids, fn id ->
      Gaia.Jobs.MigrateShareholdingsToContactsForCompany.enqueue(%{"company_profile_id" => id})
    end)
  end

  def migrate_shareholdings_to_contacts_for_company(company_profile_id) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:lock_shareholdings, fn repo, _changes ->
      # immediately lock all shareholding rows with no contact_ids
      shareholdings =
        repo.all(
          from(sh in Shareholding,
            where: sh.company_profile_id == ^company_profile_id and is_nil(sh.contact_id),
            lock: "FOR UPDATE"
          )
        )

      {:ok, shareholdings}
    end)
    |> Ecto.Multi.run(:shareholdings_to_insert_new_contacts, fn _repo,
                                                                %{
                                                                  lock_shareholdings: shareholdings
                                                                } ->
      shareholdings_to_insert_new_contacts =
        Enum.map(shareholdings, fn sh ->
          %{
            company_profile_id: company_profile_id,
            registry_holder_ids: [sh.registry_holder_id],
            inserted_at: NaiveDateTime.utc_now(:second),
            updated_at: NaiveDateTime.utc_now(:second),
            contact_source: :registry_import
          }
        end)

      {:ok, shareholdings_to_insert_new_contacts}
    end)
    |> Ecto.Multi.run(:insert_contacts, fn repo,
                                           %{
                                             shareholdings_to_insert_new_contacts: shareholdings_to_insert_new_contacts
                                           } ->
      inserted_contacts =
        shareholdings_to_insert_new_contacts
        |> Enum.chunk_every(1000)
        |> Enum.flat_map(fn chunk ->
          {_, result} = repo.insert_all(Contact, chunk, returning: true)
          result
        end)

      {:ok, inserted_contacts}
    end)
    |> Ecto.Multi.run(:match_sh_and_contacts, fn repo,
                                                 %{
                                                   lock_shareholdings: shareholdings,
                                                   insert_contacts: inserted_contacts
                                                 } ->
      contacts_by_rhid =
        inserted_contacts
        |> Enum.flat_map(fn contact ->
          Enum.map(contact.registry_holder_ids, &{&1, contact.id})
        end)
        |> Map.new()

      utc_now = NaiveDateTime.utc_now(:second)

      shareholdings_to_update =
        Enum.map(shareholdings, fn sh ->
          contact_id = Map.get(contacts_by_rhid, sh.registry_holder_id)

          %{
            id: sh.id,
            contact_id: contact_id,
            inserted_at: utc_now,
            updated_at: utc_now,
            company_profile_id: sh.company_profile_id,
            account_name: sh.account_name,
            holder_id: sh.holder_id,
            registry_holder_id: sh.registry_holder_id
          }
        end)

      shareholdings_updated =
        shareholdings_to_update
        |> Enum.chunk_every(1000)
        |> Enum.flat_map(fn chunk ->
          {_, result} =
            repo.insert_all(
              Shareholding,
              chunk,
              on_conflict: {:replace, [:contact_id]},
              conflict_target: [:id],
              returning: true
            )

          result
        end)

      {:ok, shareholdings_updated}
    end)
    |> Ecto.Multi.run(:check_counts, fn _repo,
                                        %{
                                          lock_shareholdings: shareholdings,
                                          insert_contacts: inserted_contacts,
                                          match_sh_and_contacts: shareholdings_updated
                                        } ->
      inserted_contacts_count = Enum.count(inserted_contacts)
      initial_shareholdings_count = Enum.count(shareholdings)
      shareholdings_updated_count = Enum.count(shareholdings_updated)

      if initial_shareholdings_count == shareholdings_updated_count do
        Logger.info(
          "SUCCESS: Inserted contacts count: #{inserted_contacts_count}, Shareholdings updated count: #{shareholdings_updated_count}"
        )

        {:ok,
         %{
           inserted_contacts_count: inserted_contacts_count,
           shareholdings_updated_count: shareholdings_updated_count
         }}
      else
        Logger.info(
          "MISMATCH: Inserted contacts count: #{inserted_contacts_count}, Shareholdings updated count: #{shareholdings_updated_count}"
        )

        {:error,
         %{
           inserted_contacts_count: inserted_contacts_count,
           shareholdings_updated_count: shareholdings_updated_count
         }}
      end
    end)
    |> Repo.transaction(timeout: 600_000)
  end

  def email_engagement_status_for_contact(contact_id, company_profile_id) do
    query =
      from(c in Contact,
        where:
          c.id == ^contact_id and c.company_profile_id == ^company_profile_id and
            not is_nil(c.email_engagement_score),
        select:
          fragment(
            "CASE
            WHEN ? >= (
                SELECT percentile_disc(0.66) WITHIN GROUP (ORDER BY email_engagement_score)
                FROM contacts_contacts
                WHERE email_engagement_score IS NOT NULL and company_profile_id = ?
            ) THEN 'high'
            WHEN ? >= (
                SELECT percentile_disc(0.33) WITHIN GROUP (ORDER BY email_engagement_score)
                FROM contacts_contacts
                WHERE email_engagement_score IS NOT NULL and company_profile_id = ?
            ) THEN 'medium'
            ELSE 'low'
          END",
            c.email_engagement_score,
            ^company_profile_id,
            c.email_engagement_score,
            ^company_profile_id
          )
      )

    Repo.one(query)
  end

  def upsert_scores(scores, company_profile_id) do
    updated_date = NaiveDateTime.utc_now(:second)

    lum =
      Enum.reduce(scores, %{}, fn %{contact_id: contact_id, total_score: total_score}, acc ->
        Map.put(acc, contact_id, %{
          email_engagement_score: total_score,
          updated_at: {:placeholder, :now}
        })
      end)

    contact_ids = Map.keys(lum)

    contacts = contacts_by_ids(contact_ids, company_profile_id)

    to_update =
      Enum.map(contacts, fn contact ->
        update = Map.get(lum, contact.id)

        Map.merge(
          %{
            id: contact.id,
            company_profile_id: contact.company_profile_id,
            contact_source: contact.contact_source,
            inserted_at: {:placeholder, :now}
          },
          update
        )
      end)

    results =
      to_update
      |> Enum.chunk_every(10_000)
      |> Task.async_stream(fn items ->
        Repo.insert_all(Contact, items,
          on_conflict: {:replace, [:email_engagement_score, :updated_at]},
          conflict_target: :id,
          returning: false,
          placeholders: %{now: updated_date}
        )
      end)
      |> Enum.to_list()

    Enum.reduce_while(results, :ok, fn
      {:ok, _result}, acc -> {:cont, acc}
      {:error, reason}, _acc -> {:halt, {:error, reason}}
    end)
  end
end
