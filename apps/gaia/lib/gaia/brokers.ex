defmodule Gaia.Brokers do
  @moduledoc """
  The Brokers context.

  Manages broker information dynamically from the database.
  Data structure is in a list or map depending on your need.
  """

  import Ecto.Query, warn: false

  alias Gaia.Registers.Broker
  alias Gaia.Repo

  def brokers_mapping do
    Enum.reduce(list_brokers(), %{}, fn %{pids: pids} = broker, acc ->
      Enum.reduce(pids, acc, fn pid, pid_acc -> Map.put(pid_acc, pid, broker) end)
    end)
  end

  def get_broker_by_pid(broker_pid) do
    Map.get(brokers_mapping(), broker_pid, unknown_broker())
  end

  def list_brokers do
    Broker
    |> Repo.all()
    |> Enum.group_by(&{&1.name, &1.short_name, &1.is_wholesale})
    |> Enum.map(fn {{name, short_name, _is_wholesale}, brokers} ->
      pids = Enum.map(brokers, & &1.broker_pid)

      %{
        name: name,
        name_short: short_name,
        pids: pids
      }
    end)
  end

  def get_broker_by_pid_from_db(broker_pid) do
    case Repo.get_by(<PERSON>roke<PERSON>, broker_pid: broker_pid) do
      nil ->
        unknown_broker()

      broker ->
        %{
          name: broker.name,
          name_short: broker.short_name,
          pids: [broker.broker_pid]
        }
    end
  end

  @doc """
  Creates a broker.
  """
  def create_broker(attrs \\ %{}) do
    %Broker{}
    |> Broker.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a broker.
  """
  def update_broker(%Broker{} = broker, attrs) do
    broker
    |> Broker.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a broker.
  """
  def delete_broker(%Broker{} = broker) do
    Repo.delete(broker)
  end

  def list_brokers_uk do
    [
      %{name: "AJ Bell Youinvest"},
      %{name: "AJ Bell Securities"},
      %{name: "AJ Bell Platinum"},
      %{name: "Arbuthnot Latham & Co. Limited"},
      %{name: "Arnold Stansby & Co Limited"},
      %{name: "Barclays Investment Platform"},
      %{name: "Barclays Smart Investor"},
      %{name: "Barratt & Cooke"},
      %{name: "Blankstone Sington Limited"},
      %{name: "Brewin Dolphin Limited"},
      %{name: "Canaccord Genuity Wealth (International) Limited"},
      %{name: "Canaccord Genuity Wealth Limited"},
      %{name: "Cantor Fitzgerald Ireland Limited"},
      %{name: "Capital International Limited"},
      %{name: "Cardale Asset Management Limited"},
      %{name: "Charles Stanley & Company Limited"},
      %{name: "Charles Stanley Direct"},
      %{name: "Credo Capital Limited"},
      %{name: "DEGIRO"},
      %{name: "Dowgate Capital Limited"},
      %{name: "EFG Private Bank Limited"},
      %{name: "Equiniti Financial Services Limited"},
      %{name: "EToro (UK) Ltd"},
      %{name: "Evelyn Partners Investment Services Limited"},
      %{name: "Evelyn Partners Securities"},
      %{name: "Farley & Thompson LLP"},
      %{name: "Fidelity International"},
      %{name: "FinecoBank"},
      %{name: "FinecoBank S.p.A."},
      %{name: "First Equity Limited"},
      %{name: "Fiske plc"},
      %{name: "Freetrade"},
      %{name: "Freetrade Limited"},
      %{name: "GHC Capital Markets Limited"},
      %{name: "Halifax Share Dealing"},
      %{name: "Halifax Share Dealing Limited"},
      %{name: "Hargreaves Lansdown"},
      %{name: "Hargreaves Lansdown Asset Management Limited"},
      %{name: "Hedley & Company Stockbrokers Limited"},
      %{name: "HSBC InvestDirect"},
      %{name: "Hubwise Securities Limited"},
      %{name: "IDealing.com Limited"},
      %{name: "IG Group"},
      %{name: "IG Markets Limited"},
      %{name: "Interactive Brokers"},
      %{name: "Interactive Brokers (UK) Limited"},
      %{name: "Interactive Investor Services Limited"},
      %{name: "Investec Wealth & Investment (Channel Islands) Limited"},
      %{name: "Investec Wealth & Investment Limited"},
      %{name: "IWI International Wealth Insurer"},
      %{name: "J.M. Finn & Co.Ltd"},
      %{name: "James Brearley & Sons"},
      %{name: "James Brearley & Sons Limited"},
      %{name: "James Sharp & Co."},
      %{name: "James Sharp & Co LLP"},
      %{name: "Jarvis Investment Management Limited"},
      %{name: "Killik & Co LLP"},
      %{name: "LGT Vestra LLP"},
      %{name: "Lazard & Co."},
      %{name: "Landesbank Baden-Württemberg"},
      %{name: "Lloyds Bank Share Dealing"},
      %{name: "M&G Securities"},
      %{name: "MASECO Private Wealth"},
      %{name: "MHA Caves Wealth Limited"},
      %{name: "Manchester & Cheshire Investment"},
      %{name: "Mayfair Capital"},
      %{name: "Millfield Partnership"},
      %{name: "Milford & Dormor"},
      %{name: "Mirabaud Investment Services"},
      %{name: "Moneyfarm"},
      %{name: "Netwealth"},
      %{name: "Nutmeg"},
      %{name: "Oberon Investments Limited"},
      %{name: "Oval Money"},
      %{name: "Park Square Capital"},
      %{name: "Parmenion"},
      %{name: "Pilling & Co Stockbrokers Ltd"},
      %{name: "Portfolio Management Software"},
      %{name: "Principal Investment Management"},
      %{name: "Quilter Cheviot Ltd"},
      %{name: "RBC Direct Investing"},
      %{name: "Ramsey Crookall & Co Limited"},
      %{name: "Ravenscroft (CI) Limited"},
      %{name: "Raymond James Financial International Limited"},
      %{name: "Raymond James Investment Services Limited"},
      %{name: "Redmayne Bentley"},
      %{name: "Redmayne-Bentley LLP"},
      %{name: "Revolut"},
      %{name: "Robinhood (UK)"},
      %{name: "Rowan Dartington & Co Limited"},
      %{name: "Russell Wood Limited"},
      %{name: "Saxo Bank"},
      %{name: "Self Select ISA by IG"},
      %{name: "Selftrade (formerly Equiniti)"},
      %{name: "Selftrade (not to be confused with the former Equiniti platform)"},
      %{name: "Seven Investment Management LLP"},
      %{name: "The Share Centre"},
      %{name: "Thomas Grant & Company Ltd"},
      %{name: "Trading 212"},
      %{name: "Trustnet Direct"},
      %{name: "Walker Crips Investment Management Limited"},
      %{name: "Wealthify"},
      %{name: "Willis Owen"},
      %{name: "X-O.co.uk"},
      %{name: "Zurleys"},
      %{name: "Other"},
      %{name: "I don't use a broker"}
    ]
  end

  def unknown_broker do
    %{name: "Unknown", pids: [], name_short: "Unknown"}
  end

  def unknown_broker_uk do
    %{name: "Unknown"}
  end
end
