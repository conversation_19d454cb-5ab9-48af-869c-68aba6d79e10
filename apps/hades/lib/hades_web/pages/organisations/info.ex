defmodule HadesWeb.OrganisationsLive.Info do
  @moduledoc false

  use <PERSON><PERSON>eb, :live_view
  use Helper.Pipe

  alias <PERSON>aia.BeneficialOwners
  alias Gaia.Companies
  alias Gaia.Companies.InvestorHub
  alias Gaia.Companies.Profile
  alias Gaia.Companies.ProfileUser
  alias Gaia.Companies.User
  alias <PERSON>aia.Flows
  alias Gaia.Flows.DistributionSettings
  alias Gaia.Markets.Ticker
  alias Gaia.Registers
  alias Gaia.Repo
  alias Gaia.Tracking
  alias Gaia.Uploaders.Companies.TestAnnouncementPDF
  alias Gaia.Websites
  alias Gaia.Websites.Website
  alias HadesWeb.LiveComponents.ComplianceCopy
  alias HadesWeb.LiveComponents.CustomTransactionalEmail
  alias HadesWeb.LiveComponents.EmailLogs
  alias HadesWeb.LiveComponents.Modal
  alias HadesWeb.LiveComponents.SearchForm
  alias HadesWeb.LiveComponents.ShareholderOffers
  alias HadesWeb.LiveComponents.Users
  alias HadesWeb.Organisations.Helper
  alias HadesWeb.OrganisationsLive.Assistant
  alias HadesWeb.OrganisationsLive.CreateTestAnnouncement
  alias <PERSON>Web.OrganisationsLive.CreateTestShareholder
  alias <PERSON>Web.OrganisationsLive.DNSIntegration
  alias HadesWeb.OrganisationsLive.Hub
  alias HadesWeb.OrganisationsLive.PastPlacements
  alias HadesWeb.Pages.Organisations.BeneficialOwnersReports
  alias HadesWeb.Pages.Organisations.ComputershareUpload
  alias Tracking.EmailEvent

  on_mount(HadesWeb.AdminUserLiveAuth)

  @menu :organisations

  # For table (desktop) and card list (mobile)
  @default_page_number 1
  @default_page_size 10
  @max_file_size 1024 * 1024 * 15

  def mount(params, _session, socket) do
    company_profile_id = Map.get(params, "id")
    market = Map.get(params, "market")

    if connected?(socket) do
      Phoenix.PubSub.subscribe(Gaia.PubSub, "CompanyProfiles")
      Phoenix.PubSub.subscribe(Gaia.PubSub, "Users-#{company_profile_id}")
    end

    socket =
      :new_shareholder_welcome
      |> Flows.get_active_settings_by_flow_for_company(company_profile_id)
      |> Enum.at(0)
      |> case do
        nil ->
          socket
          |> assign(:is_shareholder_welcome_email_enabled, false)
          |> assign(:has_shareholder_welcome_email_automation, false)

        %DistributionSettings{shareholder_welcome_enabled: shareholder_welcome_enabled} ->
          socket
          |> assign(:is_shareholder_welcome_email_enabled, shareholder_welcome_enabled)
          |> assign(:has_shareholder_welcome_email_automation, true)
      end

    socket = assign(socket, :organisation_offboarded, false)

    website = Websites.get_or_create_website_by_company_profile_id(company_profile_id)

    company_profile_id
    |> Companies.get_profile()
    |> Repo.preload([:investor_hub, :shareholder_offer_permission, :custom_domain, :registry_credential])
    |> case do
      %Profile{} = profile ->
        socket =
          socket
          |> assign(:container, false)
          |> assign(:menu, @menu)
          |> assign(:company_profile_id, company_profile_id)
          |> assign(:profile, profile)
          |> assign(:changeset, Profile.changeset(profile, %{}))
          |> allow_upload(:dns_integration_pdf,
            accept: ~w(.pdf),
            max_entries: 1,
            max_file_size: @max_file_size
          )
          |> assign(:announcement_created, false)
          |> assign(:header, "")
          |> assign(:announcement_type, nil)
          |> assign(:market_sensitive, false)
          |> assign(:announcement_create_error, nil)
          |> allow_upload(:test_announcement_pdf,
            accept: ~w(.pdf),
            max_entries: 1,
            max_file_size: @max_file_size
          )
          |> allow_upload(:computershare_registry_excel,
            accept: ~w(.xls .xlsx),
            max_entries: 1,
            max_file_size: @max_file_size
          )
          |> allow_upload(:beneficial_owners_report_upload,
            accept: ~w(.csv .xls .xlsx .pdf .doc .docx),
            max_entries: 1,
            max_file_size: @max_file_size
          )
          |> assign(:shareholder_created, false)
          |> assign(:shareholder_account_name, nil)
          |> assign(:shareholder_email, nil)
          |> assign(:shareholder_type, :new)
          |> assign(:create_shareholder_button_enabled, false)
          |> assign(:is_editing_transactional_email, false)
          |> assign(:send_test_transactional_email_button_enabled, false)
          |> assign(:website, website)
          |> assign(:is_editing_compliance_copy, false)
          |> assign(:assistant_session_token, 16 |> :crypto.strong_rand_bytes() |> Base.encode16(case: :lower))
          |> assign(:assistant_logged, false)
          |> assign(:max_movement_date, Gaia.Registers.get_max_share_movement_date_for_company(profile.id))

        {:ok, socket}

      _ ->
        {
          :ok,
          push_navigate(socket,
            to: Routes.live_path(socket, HadesWeb.OrganisationsLive.List, market)
          )
        }
    end
  end

  def handle_event("cancel_past_placement_form", _params, socket) do
    {:noreply, assign(socket, show_form: false)}
  end

  def handle_event("edit_past_placement", %{"id" => id}, socket) do
    id
    |> Gaia.Raises.get_past_placement()
    |> case do
      %Gaia.Raises.PastPlacement{} = past_placement ->
        {:noreply, assign(socket, show_form: true, form_data: past_placement)}

      _ ->
        {:noreply,
         socket
         |> assign(:show_form, false)
         |> put_flash(:error, "Past placement not found")}
    end
  end

  def handle_event("link_past_placement_tranches", %{"company_profile_id" => company_profile_id}, socket) do
    company_profile_id
    |> Gaia.Raises.link_past_placement_tranches()
    |> case do
      [] ->
        {:noreply, put_flash(socket, :error, "No tranches linked")}

      tranches ->
        {:noreply, put_flash(socket, :info, "#{length(tranches)} placement(s) linked")}
    end
  end

  def handle_event("sync_past_placement_participant", %{"id" => past_placement_id}, socket) do
    Gaia.Jobs.SyncPastPlacementsParticipants.enqueue(%{"past_placement_id" => past_placement_id})
    {:noreply, put_flash(socket, :info, "Syncing past placement participant")}
  end

  def handle_event("delete_past_placement", %{"id" => past_placement_id}, socket) do
    past_placement_id
    |> Gaia.Raises.get_past_placement()
    |> Gaia.Raises.delete_past_placement()
    |> case do
      {:ok, _} ->
        {:noreply, put_flash(socket, :info, "Past placement deleted")}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Failed to delete past placement")}
    end
  end

  def handle_event(
        "save_past_placement",
        %{
          "past_placement" =>
            %{
              "id" => past_placement_id,
              "offer_announced_at" => offer_announced_at,
              "outcome_announced_at" => outcome_announced_at
            } = input
        },
        socket
      )
      when not is_nil(past_placement_id) and past_placement_id != "" do
    with %Gaia.Raises.PastPlacement{} = past_placement <- Gaia.Raises.get_past_placement(past_placement_id),
         offer_announced_at = NaiveDateTime.from_iso8601!(offer_announced_at <> "T00:00:00"),
         outcome_announced_at = NaiveDateTime.from_iso8601!(outcome_announced_at <> "T00:00:00"),
         {:ok, %Gaia.Raises.PastPlacement{}} <-
           Gaia.Raises.update_past_placement(
             past_placement,
             input
             |> Map.put("offer_announced_at", offer_announced_at)
             |> Map.put("outcome_announced_at", outcome_announced_at)
           ) do
      {:noreply, assign(socket, :show_form, false)}
    else
      _error ->
        {:noreply,
         socket
         |> assign(:show_form, true)
         |> put_flash(:error, "Failed to save past placement. Please check your input and try again.")}
    end
  end

  def handle_event(
        "save_past_placement",
        %{
          "past_placement" =>
            %{"offer_announced_at" => offer_announced_at, "outcome_announced_at" => outcome_announced_at} = input
        },
        socket
      ) do
    offer_announced_at = NaiveDateTime.from_iso8601!(offer_announced_at <> "T00:00:00")
    outcome_announced_at = NaiveDateTime.from_iso8601!(outcome_announced_at <> "T00:00:00")

    updated_input =
      input
      |> Map.put("offer_announced_at", offer_announced_at)
      |> Map.put("outcome_announced_at", outcome_announced_at)

    updated_input
    |> Gaia.Raises.create_past_placement()
    |> case do
      {:ok, %Gaia.Raises.PastPlacement{id: _past_placement_id}} ->
        {:noreply, assign(socket, :show_form, false)}

      {:error, _} ->
        {:noreply,
         socket
         |> assign(:show_form, true)
         |> put_flash(:error, "Failed to save past placement. Please check your input and try again.")}
    end
  end

  def handle_event("show_past_placement_form", _params, socket) do
    {:noreply, assign(socket, show_form: true)}
  end

  def handle_event("connect_to_fake_registry", _value, %{assigns: %{profile: %Profile{ticker: %Ticker{}}}} = socket) do
    HadesWeb.OrganisationsLive.RegistryData.connect_to_fake_registry(socket)
  end

  def handle_event("connect_to_registry", _value, %{assigns: %{profile: %Profile{ticker: %Ticker{}}}} = socket) do
    HadesWeb.OrganisationsLive.RegistryData.connect_to_registry(socket)
  end

  def handle_event(
        "toggle_log_conversation",
        _,
        %{assigns: %{assistant_logged: prev_assistant_logged, assistant_session_token: assistant_session_token}} = socket
      ) do
    Gaia.Tracking.upsert_wukong_message(assistant_session_token, %{pending_check: not prev_assistant_logged})
    {:noreply, assign(socket, :assistant_logged, not prev_assistant_logged)}
  end

  def handle_event(
        "go-to-registry-data",
        _value,
        %{assigns: %{profile: %Profile{id: profile_id, ticker: %Ticker{market_key: market_key}}}} = socket
      ) do
    {:noreply,
     push_patch(socket,
       to: Routes.organisations_info_path(socket, :index, "#{market_key}", profile_id, "registry-data"),
       replace: true
     )}
  end

  def handle_event("paginate_users", params, %{assigns: %{profile: %Profile{id: profile_id}}} = socket) do
    page_number = Map.get(params, "page_number", @default_page_number)
    page_size = Map.get(params, "page_size", @default_page_size)

    %{
      paginate_opts: [page: page_number, page_size: page_size],
      preload_opts: [[profile: :ticker], :user, :title, :companies_role],
      where_opts: [profile_id: profile_id]
    }
    |> Companies.paginate_companies_profile_users()
    |> assign(
      socket,
      :profile_users_paginated,
      __
    )
    |> {:noreply, __}
  end

  def handle_event("paginate_email_logs", params, %{assigns: %{profile: %Profile{id: profile_id}}} = socket) do
    page_number = Map.get(params, "page_number", @default_page_number)
    page_size = Map.get(params, "page_size", @default_page_size)

    %{
      paginate_opts: [page: page_number, page_size: page_size],
      preload_opts: [:email],
      company_profile_id: profile_id
    }
    |> Tracking.get_paginated_emails_with_details_and_events()
    |> assign(socket, :email_logs, __)
    |> {:noreply, __}
  end

  def handle_event(
        "paginate_beneficial_owners_reports",
        params,
        %{assigns: %{profile: %Profile{id: company_profile_id}}} = socket
      ) do
    page_number = Map.get(params, "page_number", @default_page_number)
    page_size = Map.get(params, "page_size", @default_page_size)

    %{
      company_profile_id: company_profile_id,
      paginate_opts: [page: page_number, page_size: page_size]
    }
    |> Gaia.BeneficialOwners.reports_for_company_paginated()
    |> assign(
      socket,
      :beneficial_owners_reports,
      __
    )
    |> {:noreply, __}
  end

  def handle_event("submit_registry_data", data, %{assigns: %{profile: %Profile{ticker: %Ticker{}}}} = socket),
    do: HadesWeb.OrganisationsLive.RegistryData.handle_form(data, socket)

  def handle_event("validate", %{"_target" => ["dns_integration_pdf"]}, socket) do
    {_done, in_progress} = uploaded_entries(socket, :dns_integration_pdf)

    if length(in_progress) === 1 do
      {:noreply, socket}
      # Create relativelt unique filename when user is using same pdf

      # Store file

      # Get stored file url

      # When company_profile_id exists, live_action is not :index, :invite_email_resent or :delete_profile_user
      # navigate to organisation company_profile_id index page

      # When company_profile_id doesn't exist, redirect to OrganisationsLive.List page

      # TODO - Implement invite user button click event + Remove tooltip
    else
      {:noreply, drop_all_dns_uploads(socket, in_progress)}
    end
  end

  def handle_event("save-dns-pdf", _, socket) do
    case consume_uploaded_entries(socket, :dns_integration_pdf, fn %{path: path}, entry ->
           profile = socket.assigns.profile

           dns_integration_pdf = %Plug.Upload{
             path: path,
             content_type: entry.client_type,
             filename: entry.client_name
           }

           updated_profile_resp = Companies.update_profile(profile, %{dns_integration_pdf: dns_integration_pdf})

           Phoenix.PubSub.broadcast(
             Gaia.PubSub,
             "CompanyProfiles",
             {:company_profile_updated, profile.id}
           )

           updated_profile_resp
         end) do
      [%Profile{}] = [updated_profile] ->
        {:noreply, assign(socket, profile: updated_profile)}

      _ ->
        {:noreply, put_flash(socket, :error, "Failed to upload file")}
    end
  end

  def handle_event("remove-dns-pdf", _, socket) do
    case Gaia.Companies.update_profile(socket.assigns.profile, %{dns_integration_pdf: nil}) do
      {:ok, %Profile{} = updated_profile} ->
        {:noreply, assign(socket, profile: updated_profile)}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Oops! Something went wrong. Please try again.")}
    end
  end

  def handle_event("validate-announcement", %{"_target" => ["test_announcement_pdf"]}, socket) do
    {_done, in_progress} = uploaded_entries(socket, :test_announcement_pdf)

    if length(in_progress) === 1 do
      {:noreply, socket}
    else
      {:noreply,
       drop_all_announcement_uploads(
         socket,
         in_progress
       )}
    end
  end

  def handle_event("validate-announcement", _, socket), do: {:noreply, socket}

  def handle_event("remove-announcement-pdf", _, socket) do
    {_done, in_progress} = uploaded_entries(socket, :test_announcement_pdf)

    socket =
      Enum.reduce(in_progress, socket, fn upload, socket -> cancel_upload(socket, :test_announcement_pdf, upload.ref) end)

    {:noreply, assign(socket, :announcement_create_error, nil)}
  end

  def handle_event(
        "import-computershare",
        _,
        %{assigns: %{profile: %Profile{id: company_profile_id, ticker: ticker}}} = socket
      ) do
    listing_key = ticker |> Ticker.resolve_market_listing_key() |> String.upcase()

    date = Timex.format!(DateTime.utc_now(), "{YYYY}-{0M}-{0D}")

    file_name = "#{listing_key}_#{date}.xlsx"

    [{:ok, uploaded}] =
      consume_uploaded_entries(socket, :computershare_registry_excel, fn %{path: path}, _ ->
        object_name = "uploads/company_profile/#{company_profile_id}/registers/computershare/#{file_name}"

        local_path = Path.expand(path)

        {:ok, GoogleAPI.Storage.put_object(object_name, local_path)}
      end)

    case uploaded do
      %{id: _id} ->
        Gaia.Jobs.ImportManualRegistry.enqueue(%{
          "company_profile_id" => company_profile_id,
          "file_to_import" => file_name
        })

        {:noreply,
         put_flash(
           socket,
           :info,
           "Computershare upload started. It might take a while to process, please check later if everything was imported."
         )}

      _ ->
        {:noreply,
         socket
         |> assign(:import_computershare_started, false)
         |> assign(:import_computershare_error, "Failed to start the import")}
    end
  end

  def handle_event("validate-import-computershare", %{"_target" => ["computershare_registry_excel"]}, socket) do
    {_done, in_progress} = uploaded_entries(socket, :computershare_registry_excel)

    if length(in_progress) === 1 do
      {:noreply, socket}
    else
      {:noreply,
       in_progress
       |> Enum.reduce(socket, fn upload, socket ->
         cancel_upload(socket, :computershare_registry_excel, upload.ref)
       end)
       |> put_flash(:error, "Too many files")}
    end
  end

  def handle_event("validate-import-computershare", _, socket), do: {:noreply, socket}

  def handle_event("remove-import-computershare", _, socket) do
    {_done, in_progress} = uploaded_entries(socket, :computershare_registry_excel)

    socket =
      Enum.reduce(in_progress, socket, fn upload, socket ->
        cancel_upload(socket, :computershare_registry_excel, upload.ref)
      end)

    {:noreply, assign(socket, :computershare_import_error, nil)}
  end

  def handle_event(
        "import-beneficial-owners-report",
        %{"beneficial_owners_report_upload" => %{"report_id" => report_id}},
        %{assigns: %{profile: %Profile{id: company_profile_id}}} = socket
      ) do
    [{:ok, uploaded}] =
      consume_uploaded_entries(socket, :beneficial_owners_report_upload, fn %{path: path}, entry ->
        object_name =
          "uploads/company_profile/#{company_profile_id}/beneficial_owners/reports/#{report_id}/disclosed_interest_documents/#{entry.client_name}"

        local_path = Path.expand(path)

        {:ok, GoogleAPI.Storage.put_object(object_name, local_path)}
      end)

    case uploaded do
      %{name: name} ->
        filename =
          String.replace(
            name,
            "uploads/company_profile/#{company_profile_id}/beneficial_owners/reports/#{report_id}/disclosed_interest_documents/",
            ""
          )

        report = BeneficialOwners.get_report_by_id!(report_id)

        BeneficialOwners.upsert_report(report, %{
          disclosed_interest_document_uploaded_at: NaiveDateTime.utc_now(:second),
          disclosed_interest_document_filename: filename,
          type: if(report.type == :importing, do: :processing, else: report.type)
        })

        {:noreply,
         socket
         |> put_flash(:info, "File uploaded successfully")
         |> push_patch(
           to:
             Routes.organisations_info_path(
               socket,
               :index,
               socket.assigns.market,
               socket.assigns.profile.id,
               "beneficial-owners-reports"
             ),
           replace: true
         )}

      _ ->
        {:noreply,
         put_flash(
           socket,
           :error,
           "Import failed, please try again then ask devs for help."
         )}
    end
  end

  def handle_event("validate-beneficial-owners-report", %{"_target" => ["beneficial_owners_report_upload"]}, socket) do
    {_done, in_progress} = uploaded_entries(socket, :beneficial_owners_report_upload)

    if length(in_progress) === 1 do
      {:noreply, socket}
    else
      {:noreply,
       in_progress
       |> Enum.reduce(socket, fn upload, socket ->
         cancel_upload(socket, :beneficial_owners_report_upload, upload.ref)
       end)
       |> put_flash(:error, "Too many files")}
    end
  end

  def handle_event("validate-beneficial-owners-report", _, socket), do: {:noreply, socket}

  def handle_event("remove-beneficial-owners-report", _, socket) do
    {_done, in_progress} = uploaded_entries(socket, :beneficial_owners_report_upload)

    socket =
      Enum.reduce(in_progress, socket, fn upload, socket ->
        cancel_upload(socket, :beneficial_owners_report_upload, upload.ref)
      end)

    {:noreply, put_flash(socket, :info, "Upload cancelled")}
  end

  def handle_event("assistant_send_message", %{"profile" => %{"message" => message}}, socket) do
    handle_event(
      "assistant_send_message",
      %{"message" => message},
      socket
    )
  end

  def handle_event(
        "assistant_send_message",
        %{"message" => message},
        %{
          assigns:
            %{
              profile: %{id: company_profile_id},
              assistant_session_token: assistant_session_token,
              current_admin_user: %Gaia.Admins.User{id: current_admin_user_id}
            } = assigns
        } = socket
      ) do
    existing_messages = Map.get(assigns, :assistant_messages, [])

    messages = [
      %{
        role: :user,
        message: message
      },
      %{
        role: :assistant,
        message: "..."
      }
    ]

    assistant =
      Map.get(
        assigns,
        :assistant,
        HadesWeb.Helpers.Wukong.new(%{
          company_profile_id: company_profile_id,
          admin_id: current_admin_user_id,
          token: assistant_session_token
        })
      )

    socket =
      socket
      |> assign(:assistant_messages, existing_messages ++ messages)
      |> assign(:assistant, assistant)
      |> start_async(:assistant_response, fn ->
        {:ok, chain} =
          result =
          assistant
          |> LangChain.Chains.LLMChain.add_message(LangChain.Message.new_user!(message))
          |> LangChain.Chains.LLMChain.run(mode: :while_needs_response)

        Gaia.Tracking.upsert_wukong_message(assistant_session_token, %{
          messages: Gaia.AI.get_messages_log_from_chain(chain),
          company_profile_id: company_profile_id,
          admin_id: current_admin_user_id,
          token: assistant_session_token
        })

        result
      end)

    {:noreply, socket}
  end

  def handle_event(
        "create-announcement",
        %{"announcement" => announcement_input},
        %{assigns: %{profile: %Profile{ticker: %{market_key: market_key}}}} = socket
      ) do
    [{:ok, uploaded}] =
      consume_uploaded_entries(socket, :test_announcement_pdf, fn %{path: path}, entry ->
        filename = to_string(:os.system_time(:millisecond)) <> "-" <> entry.client_name

        test_announcement_pdf = %Plug.Upload{
          path: path,
          content_type: entry.client_type,
          filename: filename
        }

        {:ok, file_name} = TestAnnouncementPDF.store({test_announcement_pdf, socket.assigns.profile})
        url = TestAnnouncementPDF.url({file_name, socket.assigns.profile})

        %{
          "announcement_type" => rectype,
          "header" => header,
          "market_sensitive" => market_sensitive
        } = announcement_input

        subtypes =
          Enum.find(
            Gaia.Interactions.MediaAnnouncement.list_announcement_types(market_key),
            &(&1.value == rectype)
          ).subtypes

        category_id =
          Enum.map_join(
            subtypes,
            ",",
            & &1.value
          )

        %{ticker: %{listing_key: listing_key}} = socket.assigns.profile

        now = DateTime.utc_now()

        announcement_weblink_format = %{
          "categoryId" => category_id,
          "date" => Timex.format!(now, "{YYYY}{0M}{0D}"),
          "headlineText" => header,
          "marketSensitive" => market_sensitive,
          "pdfLink" => url,
          "symbol" => String.upcase(listing_key),
          "time" => Timex.format!(now, "{h24}{m}{s}"),
          "marketKey" => market_key
        }

        {:ok, Gaia.Interactions.test_announcement_import_duplicate_function(announcement_weblink_format)}
      end)

    case uploaded do
      %{id: _id} ->
        {:noreply, assign(socket, :announcement_created, true)}

      _ ->
        {:noreply,
         socket |> assign(:announcement_created, false) |> assign(:announcement_create_error, "Failed to create")}
    end
  end

  def handle_event("create-another-announcement", _, socket) do
    {:noreply, assign(socket, :announcement_created, false)}
  end

  def handle_event("open-organisation-offboard-confirmation-modal", _, socket) do
    {:noreply, assign(socket, :show_organisation_offboard_confirmation_modal, true)}
  end

  def handle_event("hub-migration", _payload, %{assigns: %{profile: %Profile{id: profile_id}}} = socket) do
    Gaia.Jobs.GenerateBaseWebsiteBuilderHubForCompany.enqueue(%{company_profile_id: profile_id})

    {:noreply, socket}
  end

  def handle_event(
        "disable-shareholder-welcome-email-automation",
        _payload,
        %{assigns: %{profile: %Profile{id: profile_id}}} = socket
      ) do
    :new_shareholder_welcome
    |> Flows.deactivate_distribution_settings_for_company(profile_id)
    |> case do
      {:ok, _} ->
        {:noreply, assign(socket, :is_shareholder_welcome_email_enabled, false)}

      _ ->
        {:noreply, put_flash(socket, :error, "Failed to disable shareholder welcome email automation")}
    end
  end

  def handle_event(
        "enable-shareholder-welcome-email-automation",
        _payload,
        %{assigns: %{profile: %Profile{id: profile_id}}} = socket
      ) do
    :new_shareholder_welcome
    |> Flows.activate_distribution_settings_for_company(profile_id)
    |> case do
      {:ok, _} ->
        {:noreply, assign(socket, :is_shareholder_welcome_email_enabled, true)}

      _ ->
        {:noreply, put_flash(socket, :error, "Failed to enable shareholder welcome email automation")}
    end
  end

  def handle_event(
        "validate-shareholder",
        %{"shareholder" => %{"shareholder_email" => shareholder_email} = shareholder_input},
        socket
      ) do
    is_email_valid = shareholder_email == "" or Regex.match?(~r/^[^\s]+@[^\s]+$/, shareholder_email)
    is_account_name_valid = String.trim(shareholder_input["shareholder_account_name"]) != ""
    is_type_valid = String.trim(shareholder_input["shareholder_type"]) != ""

    {:noreply,
     assign(
       socket,
       :create_shareholder_button_enabled,
       is_email_valid and is_account_name_valid and is_type_valid
     )}
  end

  def handle_event(
        "create-shareholder",
        %{"shareholder" => sharholder_input},
        %{
          assigns: %{
            profile: %Profile{id: profile_id},
            is_shareholder_welcome_email_enabled: is_shareholder_welcome_email_enabled
          }
        } = socket
      ) do
    %{
      "shareholder_account_name" => shareholder_account_name,
      "shareholder_email" => shareholder_email,
      "shareholder_type" => shareholder_type
    } = sharholder_input

    %{
      account_name: shareholder_account_name,
      email: shareholder_email,
      company_profile_id: profile_id
    }
    |> Registers.test_welcome_new_shareholder(
      String.to_atom(shareholder_type),
      is_shareholder_welcome_email_enabled
    )
    |> case do
      :ok ->
        {:noreply, socket |> assign(:shareholder_created, true) |> assign(:create_shareholder_button_enabled, false)}

      :skip ->
        {:noreply,
         socket
         |> assign(:shareholder_created, true)
         |> assign(:create_shareholder_button_enabled, false)
         |> put_flash(:info, "Welcome email automation is disabled. No email will be sent!")}

      {:error, error} ->
        {:noreply, put_flash(socket, :error, error)}
    end
  end

  def handle_event("create-another-shareholder", _, socket) do
    {:noreply, assign(socket, :shareholder_created, false)}
  end

  def handle_event(
        "edit-custom-transactional-email",
        %{
          "transactional_email_header_html" => transactional_email_header_html,
          "transactional_email_footer_html" => transactional_email_footer_html
        } = _params,
        socket
      ) do
    socket.assigns.profile.investor_hub
    |> Companies.update_investor_hub(%{
      transactional_email_header_html: transactional_email_header_html,
      transactional_email_footer_html: transactional_email_footer_html
    })
    |> case do
      {:ok, %InvestorHub{}} ->
        Phoenix.PubSub.broadcast(
          Gaia.PubSub,
          "CompanyProfiles",
          {:company_profile_investor_hub_updated, socket.assigns.profile.id}
        )

        {:noreply, assign(socket, :is_editing_transactional_email, false)}

      error ->
        {:noreply, put_flash(socket, :error, error)}
    end
  end

  def handle_event(
        "validate-test-transactional-email-address",
        %{"send_test_transactional_email" => %{"test_email_address" => test_email_address}},
        socket
      ) do
    {:noreply,
     assign(
       socket,
       :send_test_transactional_email_button_enabled,
       Regex.match?(~r/^[^\s]+@[^\s]+$/, test_email_address)
     )}
  end

  def handle_event(
        "send-test-transational-email",
        %{"send_test_transactional_email" => %{"test_email_address" => test_email_address}},
        socket
      ) do
    company_profile_logo = Helper.get_logo_url(socket.assigns.profile)

    company_profile =
      socket.assigns.profile
      |> Map.put(:logo, company_profile_logo)
      |> Repo.preload([
        :investor_hub,
        :custom_emails,
        :ticker,
        :custom_domain
      ])

    EmailTransactional.Investor
    |> Gaia.Notifications.Email.deliver(
      :test_custom_transactional_email,
      [
        company_profile,
        test_email_address,
        socket.assigns.current_admin_user.email
      ],
      company_profile.id
    )
    |> case do
      {:ok, _} ->
        {:noreply, put_flash(socket, :info, "Test transaction email sent")}

      error ->
        {:noreply, put_flash(socket, :error, error)}
    end
  end

  def handle_event(
        "edit-compliance-copy",
        %{"cookie_banner" => cookie_banner, "sign_up_page_terms_and_conditions" => sign_up_page_terms_and_conditions} =
          _params,
        socket
      ) do
    url_regex = ~r/(https?|ftp):\/\/[^\s$.?#].[^\s]*/i

    with {:has_url_regex, true} <-
           {:has_url_regex,
            [cookie_banner, sign_up_page_terms_and_conditions]
            |> Enum.filter(&(&1 !== ""))
            |> Enum.all?(&Regex.match?(url_regex, &1))},
         {:update_website, {:ok, %Website{} = updated_website}} <-
           {:update_website,
            Websites.update_website(socket.assigns.website, %{
              cookie_banner: cookie_banner,
              sign_up_page_terms_and_conditions: sign_up_page_terms_and_conditions
            })},
         {:publish_website, {:ok, %Website{} = updated_published_website}} <-
           {:publish_website, Websites.publish_website(updated_website, %{})} do
      Phoenix.PubSub.broadcast(
        Gaia.PubSub,
        "CompanyProfiles",
        {:company_profile_website_updated, socket.assigns.profile.id}
      )

      {:noreply, socket |> assign(:website, updated_published_website) |> assign(:is_editing_compliance_copy, false)}
    else
      {:has_url_regex, false} ->
        {:noreply, put_flash(socket, :warning, "You must add at least one url related to T&Cs")}

      error ->
        {:noreply, put_flash(socket, :error, error)}
    end
  end

  def handle_params(params, _url, socket) do
    # Clear any flash messages to prevent redirects
    socket = clear_flash(socket)

    # Check if we're on the hub tab and clear flash messages
    socket =
      if Map.get(params, "tab") == "hub" do
        clear_flash(socket)
      else
        socket
      end

    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  def apply_action(socket, :index, %{"id" => profile_id, "market" => market} = params) do
    tab = Map.get(params, "tab", "users")

    socket =
      socket
      |> assign(show_remove_user_modal: false)
      |> assign(show_invite_email_resent_modal: false)
      |> assign(show_organisation_offboard_confirmation_modal: false)
      |> assign(show_organisation_hub_migration_modal: false)
      |> assign(:current_profile_user, nil)
      |> assign(:is_searching, false)
      |> assign(:email_logs, nil)
      |> assign(:market, market)

    case Companies.get_profile_with_preload(profile_id, [
           :investor_hub,
           :ticker,
           :shareholder_offer_permission,
           :custom_domain,
           :registry_credential
         ]) do
      %Profile{} = profile ->
        socket
        |> assign(:breadcrumbs, [
          %{
            title: "Markets",
            to: Routes.live_path(socket, HadesWeb.OrganisationsLive.MarketList)
          },
          %{
            title: "#{String.upcase(market)} Organisations",
            to: Routes.live_path(socket, HadesWeb.OrganisationsLive.List, market)
          },
          %{
            title: profile.name,
            to: Routes.organisations_info_path(socket, :index, market, profile.id)
          }
        ])
        |> assign(:page_title, profile.name)
        |> assign(:profile, profile)
        |> assign(:shareholder_offer_permission, get_shareholder_offer_permission(profile))
        |> assign(:tab, tab)
        |> maybe_assign(:beneficial_owners_reports)
        |> assign(
          :profile_users_paginated,
          Companies.paginate_companies_profile_users(%{
            preload_opts: [[profile: :ticker], :user, :title, :companies_role],
            where_opts: [profile_id: profile.id]
          })
        )

      _ ->
        push_navigate(socket,
          to: Routes.live_path(socket, HadesWeb.OrganisationsLive.List, market)
        )
    end
  end

  def apply_action(
        %{assigns: %{profile_users_paginated: %Scrivener.Page{entries: [%ProfileUser{} | _] = entries}}} = socket,
        :invite_email_resent,
        %{"id" => _profile_id, "profile_user_id" => profile_user_id}
      ) do
    current_profile_user = Enum.find(entries, &(&1.id == String.to_integer(profile_user_id)))

    socket
    |> assign(show_invite_email_resent_modal: true)
    |> assign(:current_profile_user, current_profile_user)
  end

  def apply_action(
        %{assigns: %{profile_users_paginated: %Scrivener.Page{entries: [%ProfileUser{} | _] = entries}}} = socket,
        :delete_profile_user,
        %{"id" => _profile_id, "profile_user_id" => profile_user_id}
      ) do
    current_profile_user = Enum.find(entries, &(&1.id == String.to_integer(profile_user_id)))

    socket
    |> assign(show_remove_user_modal: true)
    |> assign(current_profile_user: current_profile_user)
  end

  def apply_action(%{assigns: %{profile: %Profile{id: profile_id}}} = socket, _live_action, %{"market" => market}) do
    push_patch(
      socket,
      to: Routes.organisations_info_path(socket, :index, market, profile_id),
      replace: true
    )
  end

  def apply_action(socket, _live_action, %{"market" => market}) do
    push_navigate(socket, to: Routes.live_path(socket, HadesWeb.OrganisationsLive.List, market))
  end

  defp maybe_assign(
         %{assigns: %{tab: "beneficial-owners-reports", profile: %{id: company_profile_id}}} = socket,
         :beneficial_owners_reports
       ) do
    beneficial_owners_reports =
      Gaia.BeneficialOwners.reports_for_company_paginated(%{
        company_profile_id: company_profile_id,
        paginate_opts: [page: @default_page_number, page_size: @default_page_size]
      })

    report_ids = Gaia.BeneficialOwners.report_ids_for_company(company_profile_id)

    beneficial_owners_report_ids = for(id <- report_ids, do: {Integer.to_string(id), id})

    socket
    |> assign(beneficial_owners_reports: beneficial_owners_reports)
    |> assign(beneficial_owners_report_ids: beneficial_owners_report_ids)
    |> assign(beneficial_owners_report_form: to_form(%{}, as: :beneficial_owners_report_upload))
  end

  defp maybe_assign(socket, _), do: socket

  def handle_info({:chat_delta, %LangChain.MessageDelta{tool_calls: [%LangChain.Message.ToolCall{} | _]}}, socket) do
    {:noreply, socket}
  end

  def handle_info({:chat_delta, %LangChain.MessageDelta{} = delta}, socket) do
    assistant_messages = socket.assigns.assistant_messages
    message = HadesWeb.Helpers.Wukong.format_streaming(delta)

    updated_messages =
      assistant_messages
      |> List.last()
      |> case do
        %{
          role: :assistant,
          message: "..."
        } ->
          %{
            role: :assistant,
            message: message
          }

        %{
          role: :assistant,
          message: existing_message
        } ->
          %{
            role: :assistant,
            message: existing_message <> message
          }
      end

    new_assistant_messages =
      assistant_messages
      |> List.delete_at(length(assistant_messages) - 1)
      |> Kernel.++([updated_messages])

    socket = assign(socket, :assistant_messages, new_assistant_messages)
    {:noreply, socket}
  end

  def handle_info({_, {:assistant_response, %{message: respond, assistant: assistant}}}, socket) do
    assistant_messages = socket.assigns.assistant_messages

    updated_messages =
      assistant_messages
      |> List.delete_at(length(assistant_messages) - 1)
      |> Kernel.++([
        %{
          role: :assistant,
          message: respond
        }
      ])

    socket = socket |> assign(:assistant_messages, updated_messages) |> assign(:assistant, assistant)
    {:noreply, socket}
  end

  def handle_info(
        {Modal, :button_clicked, %{action: "close_invite_email_resent_modal"}},
        %{assigns: %{profile: %Profile{id: profile_id}, market: market}} = socket
      ) do
    {:noreply,
     push_patch(socket,
       to: Routes.organisations_info_path(socket, :index, market, profile_id),
       replace: true
     )}
  end

  def handle_info(
        {Modal, :button_clicked,
         %{
           action: "delete_profile_user",
           param:
             %ProfileUser{user: %User{email: email}, profile: %Profile{id: profile_id, name: company_name}} = profile_user
         }},
        %{assigns: %{market: market}} = socket
      ) do
    case Companies.invalidate_profile_user(profile_user) do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "#{email} has been removed from #{company_name}")
         |> push_patch(
           to: Routes.organisations_info_path(socket, :index, market, profile_id),
           replace: true
         )}

      {:error, _} ->
        {:noreply,
         socket
         |> put_flash(
           :error,
           "Unfortunately #{email} was not removed from #{company_name} successfully, please try again later."
         )
         |> push_patch(
           to: Routes.organisations_info_path(socket, :index, market, profile_id),
           replace: true
         )}
    end
  end

  def handle_info(
        {Modal, :button_clicked, %{action: "cancel_delete_profile_user"}},
        %{assigns: %{profile: %Profile{id: profile_id}, market: market}} = socket
      ) do
    {:noreply,
     push_patch(socket,
       to: Routes.organisations_info_path(socket, :index, market, profile_id),
       replace: true
     )}
  end

  def handle_info({Modal, :button_clicked, %{action: "cancel_offboard_organisation"}}, socket) do
    {:noreply, assign(socket, :show_organisation_offboard_confirmation_modal, false)}
  end

  def handle_info(
        {Modal, :button_clicked,
         %{action: "offboard_organisation", param: %Profile{id: profile_id, name: name, invalidated: false} = _profile}},
        %{assigns: %{market: market} = assigns} = socket
      ) do
    if has_role?(assigns, :admin) do
      case Companies.offboard_company(profile_id) do
        {:ok, _} ->
          Phoenix.PubSub.broadcast(
            Gaia.PubSub,
            "FeatureFlags",
            {:company_profile_offboarded, profile_id}
          )

          {
            :noreply,
            socket
            |> push_navigate(to: Routes.live_path(socket, HadesWeb.OrganisationsLive.List, market))
            |> assign(:organisation_offboarded, true)
          }

        {:error, _} ->
          {:noreply,
           socket
           |> put_flash(
             :error,
             "Unfortunately #{name} was not offboarded successfully, please ask devs for help."
           )
           |> assign(:show_organisation_offboard_confirmation_modal, false)}
      end
    else
      {:noreply,
       socket
       |> put_flash(:error, "You do not have permission to offboard this organisation.")
       |> assign(:show_organisation_offboard_confirmation_modal, false)}
    end
  end

  def handle_info({Modal, :button_clicked, %{action: "offboard_organisation", param: %Profile{} = _profile}}, socket) do
    {:noreply, assign(socket, :show_organisation_offboard_confirmation_modal, false)}
  end

  def handle_info(
        {SearchForm, :search_input, %{search_phrase: search_phrase, action: "search_users"}},
        %{assigns: %{profile: %Profile{id: profile_id}}} = socket
      ) do
    %{
      preload_opts: [[profile: :ticker], :user, :title, :companies_role],
      where_opts: [profile_id: profile_id],
      search_phrase: search_phrase
    }
    |> Gaia.Companies.paginate_companies_profile_users()
    |> assign(
      socket,
      :profile_users_paginated,
      __
    )
    |> assign(:is_searching, true)
    |> {:noreply, __}
  end

  def handle_info(
        {SearchForm, :search_input, %{search_phrase: search_phrase, action: "search_email_logs"}},
        %{assigns: %{profile: %Profile{id: profile_id}}} = socket
      ) do
    search_result =
      Tracking.get_paginated_emails_with_details_and_events(%{
        preload_opts: [:email],
        company_profile_id: profile_id,
        search_phrase: search_phrase
      })

    {:noreply, assign(socket, :email_logs, search_result)}
  end

  def handle_info({:user_created}, socket) do
    {:noreply,
     assign(
       socket,
       :profile_users_paginated,
       Gaia.Companies.paginate_companies_profile_users(%{
         preload_opts: [[profile: :ticker], :user, :title, :companies_role],
         where_opts: [profile_id: socket.assigns.company_profile_id]
       })
     )}
  end

  def handle_info(
        {:apply_filter, %{module: module, status: status}},
        %{assigns: %{profile: %Profile{id: profile_id}}} = socket
      ) do
    filter_results =
      module
      |> get_email_logs_module_filter()
      |> Map.merge(%{
        preload_opts: [:email],
        company_profile_id: profile_id,
        where_opts: get_email_logs_status_filter(status)
      })
      |> Tracking.get_paginated_emails_with_details_and_events()

    {:noreply, assign(socket, :email_logs, filter_results)}
  end

  def handle_info({:resend_email, %EmailEvent{} = email_log}, socket) do
    email =
      email_log
      |> Repo.preload(email: [:profile])
      |> Map.get(:email)

    Gaia.Notifications.Email.deliver(EmailTransactional.Mailer, :resend_email, [email], email.company_profile_id)
    {:noreply, socket}
  end

  def handle_info({:page_duplicated, _}, %{assigns: %{tab: "hub"}} = socket) do
    # Special handling for page_duplicated event when on the hub tab
    # Don't redirect, just return the socket
    {:noreply, socket}
  end

  def handle_info({event, _}, %{assigns: %{market: market}} = socket) do
    if is_broadcast_event(event) do
      profile_with_ticker_and_shareholder_offer_permission =
        socket.assigns.profile.id
        |> Gaia.Companies.get_profile()
        |> Gaia.Repo.preload([
          :investor_hub,
          :ticker,
          :custom_domain,
          :shareholder_offer_permission,
          :registry_credential
        ])

      case profile_with_ticker_and_shareholder_offer_permission do
        %Profile{} = profile ->
          {:noreply,
           socket
           |> assign(:profile, profile)
           |> assign(:shareholder_offer_permission, get_shareholder_offer_permission(profile))}

        _ ->
          {
            :noreply,
            push_navigate(socket,
              to: Routes.live_path(socket, HadesWeb.OrganisationsLive.List, market)
            )
          }
      end
    else
      {
        :noreply,
        push_navigate(socket,
          to: Routes.live_path(socket, HadesWeb.OrganisationsLive.List, market)
        )
      }
    end
  end

  def handle_info({:flash, type, message}, socket) do
    {:noreply, put_flash(socket, type, message)}
  end

  def handle_info(_message, socket), do: {:noreply, socket}

  def handle_async(:assistant_response, {:ok, data}, socket) do
    {:ok, assistant,
     %LangChain.Message{
       content: respond
     }} = data

    assistant_messages = socket.assigns.assistant_messages

    updated_messages =
      assistant_messages
      |> List.delete_at(length(assistant_messages) - 1)
      |> Kernel.++([
        %{
          role: :assistant,
          message: respond
        }
      ])

    socket = socket |> assign(:assistant_messages, updated_messages) |> assign(:assistant, assistant)
    {:noreply, socket}
  end

  def get_email_logs_module_filter(module) do
    case module do
      "NoModule" -> %{}
      _ -> %{module_name: module}
    end
  end

  defp get_email_logs_status_filter(status) do
    case status do
      "NoStatus" -> []
      _ -> [event_type: String.to_atom(status)]
    end
  end

  defp drop_all_dns_uploads(socket, uploads) do
    uploads
    |> Enum.reduce(socket, fn upload, socket ->
      cancel_upload(socket, :dns_integration_pdf, upload.ref)
    end)
    |> put_flash(:error, "Too many files")
  end

  defp drop_all_announcement_uploads(socket, uploads) do
    uploads
    |> Enum.reduce(socket, fn upload, socket ->
      cancel_upload(socket, :announcement_pdf, upload.ref)
    end)
    |> put_flash(:error, "Too many files")
  end

  def render(assigns) do
    ~H"""
    <div class="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
      <div class="bg-white dark:bg-gray-800">
        <div class="max-w-screen-xl mx-auto p-4 sm:px-6 space-y-4">
          <div class="sm:flex items-center justify-between space-y-2 sm:space-y-0">
            <div class="flex items-center space-x-4">
              <img class="w-16 h-16 rounded-full shadow" src={Helper.get_logo_url(@profile)} />
              <div class="space-y-1 flex-1 min-w-0">
                <div class="flex items-center space-x-4">
                  <p class="typography-subtitle-1 truncate">
                    {@profile.name}
                  </p>
                  <.link navigate={
                    Routes.live_path(@socket, HadesWeb.OrganisationsLive.Edit, @market, @profile.id)
                  }>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-4 w-4 text-gray-600 dark:text-gray-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                  </.link>
                </div>
                <p class="typography-body-small text-gray-500 dark:text-gray-400 uppercase">
                  {"#{@profile.ticker.market_key}:#{@profile.ticker.listing_key}"}
                </p>
              </div>
            </div>
            <div class="flex gap-2">
              <%= if not is_nil(Application.get_env(:hubspot, :account_id)) and not is_nil(@profile.hubspot_company_id) do %>
                <PC.button
                  label="View on HubSpot"
                  to={"https://app.hubspot.com/contacts/#{Application.get_env(:hubspot, :account_id)}/record/0-2/#{@profile.hubspot_company_id}"}
                  link_type="a"
                  target="_blank"
                  color="light"
                  icon="hero-arrow-top-right-on-square"
                />
              <% end %>
              <%= if @profile.custom_domain do %>
                <PC.button
                  label="Hub"
                  to={"#{if System.get_env("LEAF_ENVIRONMENT", "development") == "production", do: "https://", else: "http://"}#{@profile.custom_domain.custom_domain}"}
                  link_type="a"
                  target="_blank"
                  color="light"
                  icon="hero-arrow-top-right-on-square"
                />
              <% end %>
              <%= if not Websites.is_hub_migrated(@profile) do %>
                <PC.button
                  label="Migrate hub"
                  color="light"
                  data-confirm="It will take a couple of minutes to migrate your existing hub pages. Once finished, a confirmation email will be sent to CSM then you can start to edit your new Hub 2.0."
                  phx-click="hub-migration"
                />
              <% end %>
              <PC.button
                label="BO Reports"
                link_type="live_redirect"
                to={
                  Routes.organisations_info_path(
                    @socket,
                    :index,
                    @market,
                    @profile.id,
                    "boreport"
                  )
                }
                color="light"
                icon="hero-document-text"
              />
              <%= if has_role?(assigns, :admin) do %>
                <PC.button
                  disabled={@organisation_offboarded}
                  label="Organisation Offboard"
                  color="danger"
                  phx-click="open-organisation-offboard-confirmation-modal"
                />
              <% end %>
            </div>
          </div>
          <div class="flex border-b border-gray-300 dark:border-gray-700 overflow-auto">
            <.link
              patch={Routes.organisations_info_path(@socket, :index, @market, @profile.id, "users")}
              class={"py-2 px-3  border-b-[3px] whitespace-nowrap hover:opacity-80 #{if @tab == "users", do: "border-status-green", else: "border-transparent"}"}
            >
              <p class="typography-body-regular">Users</p>
            </.link>

            <.link
              patch={
                Routes.organisations_info_path(@socket, :index, @market, @profile.id, "registry-data")
              }
              class={"py-2 px-3  border-b-[3px] whitespace-nowrap hover:opacity-80 #{if @tab == "registry-data", do: "border-status-green", else: "border-transparent"}"}
            >
              <p class="typography-body-regular">Registry data</p>
            </.link>

            <.link
              patch={
                Routes.organisations_info_path(
                  @socket,
                  :index,
                  @market,
                  @profile.id,
                  "dns-integration"
                )
              }
              class={"py-2 px-3  border-b-[3px] whitespace-nowrap hover:opacity-80 #{if @tab == "dns-integration", do: "border-status-green", else: "border-transparent"}"}
            >
              <p class="typography-body-regular">DNS</p>
            </.link>

            <.link
              patch={
                Routes.organisations_info_path(
                  @socket,
                  :index,
                  @market,
                  @profile.id,
                  "shareholder-offers"
                )
              }
              class={"relative gap-2 py-2 px-3 border-b-[3px] whitespace-nowrap hover:opacity-80 #{if @tab == "shareholder-offers", do: "border-status-green", else: "border-transparent"}"}
            >
              <p class="typography-body-regular">SPP</p>
            </.link>

            <.link
              patch={
                Routes.organisations_info_path(
                  @socket,
                  :index,
                  @market,
                  @profile.id,
                  "custom-transactional-email"
                )
              }
              class={"relative gap-2 py-2 px-3 border-b-[3px] whitespace-nowrap hover:opacity-80 #{if @tab == "custom-transactional-email", do: "border-status-green", else: "border-transparent"}"}
            >
              <p class="typography-body-regular">Custom Transactional Email</p>
            </.link>

            <.link
              patch={
                Routes.organisations_info_path(
                  @socket,
                  :index,
                  @market,
                  @profile.id,
                  "compliance-copy"
                )
              }
              class={"relative gap-2 py-2 px-3 border-b-[3px] whitespace-nowrap hover:opacity-80 #{if @tab == "compliance-copy", do: "border-status-green", else: "border-transparent"}"}
            >
              <p class="typography-body-regular">Compliance Copy</p>
            </.link>

            <.link
              patch={
                Routes.organisations_info_path(
                  @socket,
                  :index,
                  @market,
                  @profile.id,
                  "past-placements"
                )
              }
              class={"py-2 px-3  border-b-[3px] whitespace-nowrap hover:opacity-80 #{if @tab == "past-placements", do: "border-status-green", else: "border-transparent"}"}
            >
              <p class="typography-body-regular">Past Placements</p>
            </.link>

            <%= if Enum.member?(["development", "staging"], Application.get_env(:helper, :runtime_env)) or @profile.is_demo do %>
              <.link
                patch={
                  Routes.organisations_info_path(
                    @socket,
                    :index,
                    @market,
                    @profile.id,
                    "create-test-announcement"
                  )
                }
                class={"py-2 px-3  border-b-[3px] whitespace-nowrap hover:opacity-80 #{if @tab == "create-test-announcement", do: "border-status-green", else: "border-transparent"}"}
              >
                <p class="typography-body-regular">Test Announcement</p>
              </.link>
              <%!-- Shareholder creation is only for premium account --%>
              <%= if @profile.is_premium do %>
                <.link
                  patch={
                    Routes.organisations_info_path(
                      @socket,
                      :index,
                      @market,
                      @profile.id,
                      "create-test-shareholder"
                    )
                  }
                  class={"py-2 px-3  border-b-[3px] whitespace-nowrap hover:opacity-80 #{if @tab == "create-test-shareholder", do: "border-status-green", else: "border-transparent"}"}
                >
                  <p class="typography-body-regular">Test Shareholder</p>
                </.link>
              <% end %>
            <% end %>

            <.link
              patch={
                Routes.organisations_info_path(@socket, :index, @market, @profile.id, "assistant")
              }
              class={"py-2 px-3  border-b-[3px] whitespace-nowrap hover:opacity-80 #{if @tab == "assistant", do: "border-status-green", else: "border-transparent"}"}
            >
              <p class="typography-body-regular">Wukong (v0.6)</p>
            </.link>

            <.link
              patch={Routes.organisations_info_path(@socket, :index, @market, @profile.id, "hub")}
              class={"py-2 px-3  border-b-[3px] whitespace-nowrap hover:opacity-80 #{if @tab == "hub", do: "border-status-green", else: "border-transparent"}"}
            >
              <p class="typography-body-regular">Hub</p>
            </.link>
          </div>
        </div>
      </div>
      <div class="max-w-screen-xl mx-auto p-4 sm:p-6 overflow-visible">
        <%!-- We don't check against the market_key for these info tabs since company_profile_ids will probably never overlap between markets, but putting this down for posterity --%>
        <%= case @tab do %>
          <% "users" -> %>
            {render_users(assigns)}
          <% "registry-data" -> %>
            <.live_component
              module={HadesWeb.OrganisationsLive.RegistryData}
              id="registry-data"
              {assigns}
            />
          <% "dns-integration" -> %>
            {render_dns_integration(assigns)}
          <% "email-logs" -> %>
            {render_email_logs(assigns)}
          <% "shareholder-offers" -> %>
            {render_shareholder_offers(assigns)}
          <% "custom-transactional-email" -> %>
            {render_custom_transactional_email(assigns)}
          <% "compliance-copy" -> %>
            {render_compliance_copy(assigns)}
          <% "create-test-announcement" -> %>
            {render_create_test_announcement(assigns)}
          <% "create-test-shareholder" -> %>
            {render_create_test_shareholder(assigns)}
          <% "assistant" -> %>
            {render_assistant(assigns)}
          <% "past-placements" -> %>
            {render_past_placements(assigns)}
          <% "computershare-registry-import" -> %>
            {render_registry_import(assigns)}
          <% "beneficial-owners-reports" -> %>
            {render_beneficial_owners_reports(assigns)}
          <% "hub" -> %>
            {render_hub(assigns)}
        <% end %>
      </div>
    </div>
    """
  end

  defp render_users(assigns) do
    ~H"""
    <div class="sm:flex items-center">
      <.render_invite_user_button
        class="block sm:hidden mb-4 w-full"
        no_user_yet={Enum.empty?(@profile_users_paginated)}
      />
      <.live_component
        module={SearchForm}
        id="org-user-search-bar"
        placeholder="Find user"
        action="search_users"
      />
      <.render_invite_user_button
        class="hidden sm:block w-[106px]"
        no_user_yet={Enum.empty?(@profile_users_paginated)}
      />
    </div>

    <.live_component
      module={Users.Table}
      id="org-user-table"
      current_admin_user={@current_admin_user}
      profile_users_paginated={@profile_users_paginated}
      global={false}
      is_searching={@is_searching}
      company_profile_id={@profile.id}
      company_profile_logo={Helper.get_logo_url(@profile)}
      market={@market}
    />
    <.live_component
      module={Users.CardList}
      id="org-user-card-list"
      profile_users_paginated={@profile_users_paginated}
      global={false}
      is_searching={@is_searching}
      market={@market}
    />

    <%= if @show_remove_user_modal do %>
      <.live_component
        module={Modal}
        id="user-table-confirm-remove-user-modal"
        title="Remove user from organisation?"
        body={
          "Are you sure you want to remove #{@current_profile_user.user.email} from #{@current_profile_user.profile.name}? This action is not reversible and the user will lose access to #{@current_profile_user.profile.name} dashboard."
        }
        right_button="Yes, remove user"
        right_button_action="delete_profile_user"
        right_button_class="bg-status-red text-white text-base font-semibold rounded-lg px-4 py-3"
        right_button_param={@current_profile_user}
        left_button="No, return to dashboard"
        left_button_action="cancel_delete_profile_user"
      />
    <% end %>

    <%= if @show_invite_email_resent_modal do %>
      <.live_component
        module={Modal}
        id="user-table-confirm-invite-email-resent-modal"
        title="Invite resent"
        body={"An invite link has been sent to #{@current_profile_user.user.email}."}
        right_button="Done"
        right_button_action="close_invite_email_resent_modal"
      />
    <% end %>

    <%= if @show_organisation_offboard_confirmation_modal do %>
      <.live_component
        module={Modal}
        id="organisation-offboard-confirmation-modal"
        title={"Are you sure you want to offboard #{@profile.name}?"}
        body="Company access and third-party connections(custom domain, registry) will be removed from InvestorHub. This action is not reversible.  Ensure you have checked all the details before confirming."
        right_button="Yes, offboard organisation"
        right_button_action="offboard_organisation"
        right_button_class="bg-red-600 text-white text-base font-semibold rounded-lg px-4 py-3"
        right_button_param={@profile}
        disabled_right_button={@organisation_offboarded}
        left_button="Cancel"
        left_button_action="cancel_offboard_organisation"
      />
    <% end %>
    """
  end

  defp render_invite_user_button(assigns) do
    ~H"""
    <PC.button
      label="Invite user"
      color="light"
      disabled={true}
      class={@class}
      tooltip={
        if @no_user_yet do
          "Please invite the primary user first by clicking the button below."
        else
          "Coming soon. Please simulate an admin user below to invite a new user."
        end
      }
    />
    """
  end

  defp render_email_logs(assigns) do
    ~H"""
    <.live_component
      module={EmailLogs.Table}
      id="org-email-logs-table"
      company_profile_id={@profile.id}
      email_logs={@email_logs}
    />
    """
  end

  defp render_dns_integration(assigns) do
    ~H"""
    <.live_component module={DNSIntegration} id="dns-integration" {assigns} />
    """
  end

  defp render_create_test_announcement(assigns) do
    if Enum.member?(["development", "staging"], Application.get_env(:helper, :runtime_env)) or assigns.profile.is_demo do
      ~H"""
      <.live_component module={CreateTestAnnouncement} id="create-test-announcement" {assigns} />
      """
    end
  end

  defp render_create_test_shareholder(assigns) do
    # Shareholder creation is only for premium account
    if assigns.profile.is_premium and
         (Enum.member?(["development", "staging"], Application.get_env(:helper, :runtime_env)) or assigns.profile.is_demo) do
      ~H"""
      <.live_component module={CreateTestShareholder} id="create-test-shareholder" {assigns} />
      """
    end
  end

  defp render_assistant(assigns) do
    ~H"""
    <.live_component module={Assistant} id="assistant" {assigns} />
    """
  end

  defp render_past_placements(assigns) do
    ~H"""
    <.live_component module={PastPlacements} id="past-placements" {assigns} />
    """
  end

  defp render_registry_import(assigns) do
    ~H"""
    <.live_component module={ComputershareUpload} id="registry-import" {assigns} />
    """
  end

  defp render_beneficial_owners_reports(assigns) do
    ~H"""
    <.live_component module={BeneficialOwnersReports} id="beneficial-owners-reports" {assigns} />
    """
  end

  defp render_shareholder_offers(assigns) do
    ~H"""
    <div class="bg-white dark:bg-gray-800 p-8">
      <.live_component
        module={ShareholderOffers}
        id={"#{@profile.id}-shareholder-offers"}
        profile={@profile}
        shareholder_offer_permission={@shareholder_offer_permission}
      />
    </div>
    """
  end

  defp render_custom_transactional_email(assigns) do
    ~H"""
    <div class="bg-white dark:bg-gray-800 p-8">
      <.live_component
        module={CustomTransactionalEmail}
        id={"#{@profile.id}-custom-transactional-email"}
        profile={@profile}
        send_test_transactional_email_button_enabled={@send_test_transactional_email_button_enabled}
        is_editing_transactional_email={@is_editing_transactional_email}
      />
    </div>
    """
  end

  defp render_compliance_copy(assigns) do
    ~H"""
    <div class="bg-white dark:bg-gray-800 p-8">
      <.live_component
        module={ComplianceCopy}
        id={"#{@profile.id}-compliance-copy"}
        profile={@profile}
        website={@website}
        is_editing_compliance_copy={@is_editing_compliance_copy}
      />
    </div>
    """
  end

  defp render_hub(assigns) do
    ~H"""
    {live_render(@socket, Hub, id: "hub", session: %{"profile_id" => @profile.id})}
    """
  end

  defp get_shareholder_offer_permission(profile) do
    Map.get(profile, :shareholder_offer_permission)
  end

  def is_broadcast_event(event) do
    events = [
      :company_profile_created,
      :company_profile_updated,
      :company_profile_deleted,
      :company_profile_offboarded,
      :company_profile_investor_hub_updated,
      :company_profile_website_updated,
      :key_insights_updated,
      :industry_insights_updated
    ]

    Enum.member?(events, event)
  end
end
