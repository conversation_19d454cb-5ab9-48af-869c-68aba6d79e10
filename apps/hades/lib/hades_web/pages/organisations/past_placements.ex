defmodule HadesWeb.OrganisationsLive.PastPlacements do
  @moduledoc false
  use HadesWeb, :live_component

  def mount(socket) do
    {:ok, assign(socket, show_form: false, form_data: %{}, selected_past_placement_ids: [])}
  end

  def update(assigns, socket) do
    past_placements =
      Gaia.Raises.list_past_placements_by_company_profile_id(assigns.company_profile_id)

    replied_socket =
      socket
      |> assign(past_placements: past_placements)
      |> assign(company_profile_id: assigns.company_profile_id)
      |> assign(form_data: Map.get(assigns, :form_data, %{}))
      |> assign(selected_past_placement_ids: Map.get(assigns, :selected_past_placement_ids, []))

    replied_socket =
      if Map.has_key?(assigns, :show_form),
        do: assign(replied_socket, :show_form, assigns.show_form),
        else: replied_socket

    {:ok, replied_socket}
  end

  def render(assigns) do
    ~H"""
    <div class="overflow-x-auto bg-white dark:bg-gray-900">
      <%= unless @show_form do %>
        <div class="flex justify-end gap-2 mb-4">
          <.add />
          <.link_tranche company_profile_id={@company_profile_id} />
        </div>
      <% end %>

      <%= if @show_form do %>
        <.render_form form_data={@form_data} company_profile_id={@company_profile_id} />
      <% else %>
        <.table past_placements={@past_placements} />
      <% end %>
    </div>
    """
  end

  defp link_tranche(assigns) do
    ~H"""
    <div class="flex justify-end mb-4">
      <button
        class="btn btn-primary flex items-center gap-2"
        type="button"
        phx-click="link_past_placement_tranches"
        phx-value-company_profile_id={@company_profile_id}
      >
        Link Tranches
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          stroke-width="2"
          style="vertical-align: middle;"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M16.5 7.5l-7 7a3.5 3.5 0 01-5-5l7-7a3.5 3.5 0 015 5l-7 7"
          />
        </svg>
      </button>
    </div>
    """
  end

  defp add(assigns) do
    ~H"""
    <div class="flex justify-end mb-4">
      <button
        class="btn btn-primary flex items-center gap-2"
        type="button"
        phx-click="show_past_placement_form"
      >
        Add
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          stroke-width="2"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
        </svg>
      </button>
    </div>
    """
  end

  defp render_form(assigns) do
    ~H"""
    <form phx-submit="save_past_placement" class="bg-white dark:bg-gray-800 rounded-lg p-4">
      <div class="space-y-4">
        <input type="hidden" name="past_placement[company_profile_id]" value={@company_profile_id} />

        <%= if Map.has_key?(@form_data, :id) do %>
          <input type="hidden" name="past_placement[id]" value={Map.get(@form_data, :id)} />
        <% end %>

        <div>
          <label for="amount_raised" class="block">Amount Raised</label>
          <input
            type="number"
            step="0.01"
            name="past_placement[amount_raised]"
            class="input"
            value={Map.get(@form_data, :amount_raised, 0)}
          />
        </div>
        <div>
          <label for="issue_price" class="block">Issue Price</label>
          <input
            type="number"
            step="0.001"
            name="past_placement[issue_price]"
            class="input"
            value={Map.get(@form_data, :issue_price, 0)}
          />
        </div>
        <div>
          <label for="offer_announced_at" class="block">Offer Announced At</label>
          <input
            type="date"
            name="past_placement[offer_announced_at]"
            class="input"
            value={format_date(Map.get(@form_data, :offer_announced_at))}
          />
        </div>
        <div>
          <label for="outcome_announced_at" class="block">Outcome Announced At</label>
          <input
            type="date"
            name="past_placement[outcome_announced_at]"
            class="input"
            value={format_date(Map.get(@form_data, :outcome_announced_at))}
          />
        </div>
        <div>
          <label for="settled_at" class="block">Settled At</label>
          <input
            type="date"
            name="past_placement[settled_at]"
            class="input"
            value={Map.get(@form_data, :settled_at, "")}
          />
        </div>
        <div>
          <label for="shares_issued" class="block">Shares Issued</label>
          <input
            type="number"
            name="past_placement[shares_issued]"
            class="input"
            value={Map.get(@form_data, :shares_issued, 0)}
          />
        </div>
        <div>
          <label for="trading_halt_price" class="block">Trading Halt Price</label>
          <input
            type="number"
            step="0.001"
            name="past_placement[trading_halt_price]"
            class="input"
            value={Map.get(@form_data, :trading_halt_price, 0)}
          />
        </div>
        <div>
          <label for="tranche" class="block">
            Tranche (If two tranches, please create two separate placements)
          </label>
          <select name="past_placement[tranche]" class="input">
            <option value="none" selected={Map.get(@form_data, :tranche) == :none}>None</option>
            <option value="t1" selected={Map.get(@form_data, :tranche) == :t1}>T1</option>
            <option value="t2" selected={Map.get(@form_data, :tranche) == :t2}>T2</option>
          </select>
        </div>
        <div class="flex justify-end gap-2">
          <button type="submit" class="btn btn-primary">Save</button>
          <button type="button" class="btn btn-secondary" phx-click="cancel_past_placement_form">
            Cancel
          </button>
        </div>
      </div>
    </form>
    """
  end

  defp table(assigns) do
    ~H"""
    <%= if Enum.empty?(@past_placements) do %>
      <p class="text-center py-4 text-gray-700 dark:text-gray-300">No Past Placements available</p>
    <% else %>
      <table class="min-w-full bg-white dark:bg-gray-800">
        <thead>
          <tr>
            <th class="py-2 px-4 border-b text-left text-gray-700 dark:text-gray-300">
              Offer Announced At
            </th>
            <th class="py-2 px-4 border-b text-left text-gray-700 dark:text-gray-300">Settled At</th>
            <th class="py-2 px-4 border-b text-left text-gray-700 dark:text-gray-300">
              Shares Issued
            </th>
            <th class="py-2 px-4 border-b text-left text-gray-700 dark:text-gray-300">Issue Price</th>
            <th class="py-2 px-4 border-b text-left text-gray-700 dark:text-gray-300">Tranche</th>
            <th class="py-2 px-4 border-b text-left text-gray-700 dark:text-gray-300">Actions</th>
          </tr>
        </thead>
        <tbody>
          <%= for past_placement <- @past_placements do %>
            <tr class="bg-white dark:bg-gray-900">
              <td class="py-2 px-4 border-b text-left text-gray-700 dark:text-gray-300">
                {format_date(past_placement.offer_announced_at)}
              </td>
              <td class="py-2 px-4 border-b text-left text-gray-700 dark:text-gray-300">
                {past_placement.settled_at}
              </td>
              <td class="py-2 px-4 border-b text-left text-gray-700 dark:text-gray-300">
                {past_placement.shares_issued}
              </td>
              <td class="py-2 px-4 border-b text-left text-gray-700 dark:text-gray-300">
                {past_placement.issue_price}
              </td>
              <td class="py-2 px-4 border-b text-left text-gray-700 dark:text-gray-300">
                <%= if past_placement.tranche == :none do %>
                  none
                <% else %>
                  <%= if is_nil(past_placement.tranche_linked_id) and past_placement.tranche in [:t1, :t2] do %>
                    {"#{past_placement.tranche} (not linked)"}
                  <% else %>
                    {"#{past_placement.tranche} (linked)"}
                  <% end %>
                <% end %>
              </td>
              <td class="py-2 px-4 border-b text-left">
                <div class="flex space-x-2">
                  <button
                    phx-click="edit_past_placement"
                    phx-value-id={past_placement.id}
                    class="btn btn-secondary"
                  >
                    Edit
                  </button>
                  <button
                    phx-click="sync_past_placement_participant"
                    phx-value-id={past_placement.id}
                    class="btn btn-secondary"
                  >
                    Sync Participants
                  </button>
                  <button
                    phx-click="delete_past_placement"
                    phx-value-id={past_placement.id}
                    class="btn btn-danger"
                    aria-label="Delete"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-red-600 dark:text-red-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M10 3h4a1 1 0 011 1v1H9V4a1 1 0 011-1z"
                      />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% end %>
    """
  end
end
