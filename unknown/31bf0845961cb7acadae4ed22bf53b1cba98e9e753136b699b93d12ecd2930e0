defmodule Gaia.Registers.Broker do
  @moduledoc """
  A broker represents a financial institution that facilitates
  trading of securities. Each broker has a unique PID (Participant ID)
  and may be classified as wholesale or retail.
  """
  use Ecto.Schema

  import Ecto.Changeset

  @fields [
    :name,
    :short_name,
    :broker_pid,
    :is_wholesale
  ]

  schema "registers_brokers" do
    field(:name, :string)
    field(:short_name, :string)
    field(:broker_pid, :string)
    field(:is_wholesale, :boolean, default: false)

    timestamps()
  end

  def changeset(broker, attrs) do
    broker
    |> cast(attrs, @fields)
    |> validate_required([:name, :short_name, :broker_pid, :is_wholesale])
    |> unique_constraint(:broker_pid)
  end
end
