defmodule Gaia.Registers.ContactNamesTest do
  use Gaia.DataCase, async: false

  alias Gaia.Contacts.Contact
  alias Gaia.Registers

  describe "insert_all_shareholdings_and_link_to_contact/3 with contact names" do
    setup :company_profile_builder

    test "creates contacts with first_name from account_name", %{company_profile: company_profile} do
      company_profile_id = company_profile.id
      utc_now = NaiveDateTime.truncate(NaiveDateTime.utc_now(), :second)

      shareholdings = [
        %{
          company_profile_id: company_profile_id,
          account_name: "<PERSON>",
          holder_id: "X000000000001",
          registry_holder_id: "*************",
          email: "<EMAIL>",
          phone_number: "**********",
          inserted_at: utc_now,
          updated_at: utc_now
        },
        %{
          company_profile_id: company_profile_id,
          account_name: "ACME Corporation",
          holder_id: "X000000000002",
          registry_holder_id: "*************",
          email: "<EMAIL>",
          phone_number: "**********",
          inserted_at: utc_now,
          updated_at: utc_now
        },
        %{
          company_profile_id: company_profile_id,
          account_name: "  <PERSON>  ",
          holder_id: "X000000000003",
          registry_holder_id: "*************",
          email: nil,
          phone_number: "**********",
          inserted_at: utc_now,
          updated_at: utc_now
        }
      ]

      {:ok, _result} = Registers.insert_all_shareholdings_and_link_to_contact(shareholdings, company_profile_id)

      contacts =
        Contact
        |> where(company_profile_id: ^company_profile_id)
        |> Repo.all()

      assert length(contacts) == 3

      john_contact = Enum.find(contacts, &("*************" in &1.registry_holder_ids))
      acme_contact = Enum.find(contacts, &("*************" in &1.registry_holder_ids))
      jane_contact = Enum.find(contacts, &("*************" in &1.registry_holder_ids))

      assert john_contact.first_name == "John Doe"
      assert john_contact.email == "<EMAIL>"

      assert acme_contact.first_name == "ACME Corporation"
      assert acme_contact.email == "<EMAIL>"

      assert jane_contact.first_name == "Jane Smith"
      assert jane_contact.email == nil
    end

    test "extract_contact_first_name/1 helper function", %{} do
      assert Registers.extract_contact_first_name("John Doe") == "John Doe"
      assert Registers.extract_contact_first_name("  Jane Smith  ") == "Jane Smith"
      assert Registers.extract_contact_first_name("ACME Corporation") == "ACME Corporation"
      assert Registers.extract_contact_first_name("") == nil
      assert Registers.extract_contact_first_name("   ") == nil
      assert Registers.extract_contact_first_name(nil) == nil
      assert Registers.extract_contact_first_name(123) == nil
    end
  end
end
