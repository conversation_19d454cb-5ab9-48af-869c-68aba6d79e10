defmodule Gaia.BrokersTest do
  use Gaia.DataCase

  alias Gaia.Brokers
  alias Gaia.Registers.Broker

  describe "create_broker/1" do
    test "creates a broker with valid attributes" do
      attrs = %{
        name: "Test Broker Ltd",
        short_name: "Test Broker",
        broker_pid: "12345",
        is_wholesale: true
      }

      assert {:ok, %Broker{} = broker} = Brokers.create_broker(attrs)
      assert broker.name == "Test Broker Ltd"
      assert broker.short_name == "Test Broker"
      assert broker.broker_pid == "12345"
      assert broker.is_wholesale == true
    end

    test "returns error with invalid attributes" do
      attrs = %{name: nil}
      assert {:error, %Ecto.Changeset{}} = Brokers.create_broker(attrs)
    end

    test "returns error with duplicate broker_pid" do
      attrs = %{
        name: "Test Broker Ltd",
        short_name: "Test Broker",
        broker_pid: "12345",
        is_wholesale: false
      }

      assert {:ok, _broker} = Brokers.create_broker(attrs)
      assert {:error, %Ecto.Changeset{}} = Brokers.create_broker(attrs)
    end
  end

  describe "list_brokers/0" do
    test "returns empty list when no brokers exist" do
      assert Brokers.list_brokers() == []
    end

    test "returns brokers grouped by name and short_name" do
      # Create brokers with same name but different PIDs
      {:ok, _broker1} =
        Brokers.create_broker(%{
          name: "Test Broker Ltd",
          short_name: "Test Broker",
          broker_pid: "12345",
          is_wholesale: false
        })

      {:ok, _broker2} =
        Brokers.create_broker(%{
          name: "Test Broker Ltd",
          short_name: "Test Broker",
          broker_pid: "45678",
          is_wholesale: false
        })

      {:ok, _broker3} =
        Brokers.create_broker(%{
          name: "Another Broker Ltd",
          short_name: "Another Broker",
          broker_pid: "54321",
          is_wholesale: true
        })

      brokers = Brokers.list_brokers()
      assert length(brokers) == 2

      test_broker = Enum.find(brokers, &(&1.name == "Test Broker Ltd"))
      assert test_broker.name_short == "Test Broker"
      assert Enum.sort(test_broker.pids) == ["12345", "45678"]

      another_broker = Enum.find(brokers, &(&1.name == "Another Broker Ltd"))
      assert another_broker.name_short == "Another Broker"
      assert another_broker.pids == ["54321"]
    end
  end

  describe "get_broker_by_pid_from_db/1" do
    test "returns unknown_broker when broker not found" do
      result = Brokers.get_broker_by_pid_from_db("NONEXISTENT")
      assert result == Brokers.unknown_broker()
    end

    test "returns broker when found" do
      {:ok, _broker} =
        Brokers.create_broker(%{
          name: "Test Broker Ltd",
          short_name: "Test Broker",
          broker_pid: "12345",
          is_wholesale: true
        })

      result = Brokers.get_broker_by_pid_from_db("12345")
      assert result.name == "Test Broker Ltd"
      assert result.name_short == "Test Broker"
      assert result.pids == ["12345"]
    end
  end
end
