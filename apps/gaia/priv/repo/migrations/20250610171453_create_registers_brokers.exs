defmodule Gaia.Repo.Migrations.CreateRegistersBrokers do
  use Ecto.Migration

  def change do
    create table(:registers_brokers) do
      add :name, :string, null: false
      add :short_name, :string, null: false
      add :broker_pid, :string, null: false
      add :is_wholesale, :boolean, default: false, null: false

      timestamps()
    end

    create unique_index(:registers_brokers, [:broker_pid])
  end
end
