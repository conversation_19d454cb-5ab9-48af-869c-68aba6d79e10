defmodule Gaia.Registers do
  @moduledoc """
  The Registers context.
  """

  import Ecto.Query, warn: false

  alias <PERSON><PERSON><PERSON>.Multi
  alias Gaia.API.Token
  alias Gaia.Brokers
  alias Gaia.Comms.ContactGlobalUnsubscribe
  alias Gaia.Comms.ContactUnsubscribe
  alias <PERSON>aia.Companies
  alias Gaia.Companies.Profile
  alias Gaia.Contacts.Contact
  alias Gaia.Markets
  alias Gaia.Markets.TimeseriesNonAdjusted
  alias Gaia.Raises.PastPlacements.Participant
  alias Gaia.Registers.DailyHolding
  alias Gaia.Registers.Shareholding
  alias Gaia.Registers.ShareMovement
  alias Gaia.Repo

  require Logger

  @movement_type_consolidation [
    "CapitalReconstruction",
    "CapitalReconstructionConsolidation",
    "Consolidation"
  ]

  def movement_type_consolidation, do: @movement_type_consolidation

  @doc """
  Returns the list of shareholding.

  ## Examples

      iex> list_shareholdings()
      [%Shareholding{}, ...]

  """
  def list_shareholdings do
    Repo.all(Shareholding)
  end

  @doc """
  Returns the list of shareholding for a given company_profile_id.

  ## Examples

      iex> list_shareholdings_for_company_profile(1)
      [%Shareholding{}, ...]

  """
  def list_shareholdings_for_company_profile(%Profile{id: id}), do: list_shareholdings_for_company_profile(id)

  def list_shareholdings_for_company_profile(company_profile_id) do
    Repo.all(
      from(sh in Shareholding,
        where: sh.company_profile_id == ^company_profile_id
      )
    )
  end

  @doc """
  Gets a single shareholding.

  Raises `Ecto.NoResultsError` if the Shareholding does not exist.

  ## Examples

      iex> get_shareholding!(123)
      %Shareholding{}

      iex> get_shareholding!(456)
      ** (Ecto.NoResultsError)

  """
  def get_shareholding!(id), do: Repo.get!(Shareholding, id)

  def get_shareholding(id), do: Repo.get(Shareholding, id)

  def batch_get_shareholdings(_, ids) do
    Shareholding
    |> where([sh], sh.id in ^ids)
    |> Repo.all()
  end

  def get_shareholding_by(attrs), do: Repo.get_by(Shareholding, attrs)

  @doc """
  Get shareholders without demographic stats (age_median, income_mean...) added
  This function is used in job which fetches the demographic stat data
  """
  def get_current_shareholdings_without_demo_stats_by_company_profile_id(company_profile_id) do
    Repo.all(
      from(sh in Shareholding,
        where:
          sh.company_profile_id == ^company_profile_id and sh.share_count > 0 and
            is_nil(sh.age_median) and
            sh.address_country == ^"AUSTRALIA" and
            not (ilike(coalesce(sh.address_line_one, ""), "%po_box%") or
                   ilike(coalesce(sh.address_line_two, ""), "%po_box%")) and
            not is_nil(sh.address_postcode)
      )
    )
  end

  def get_shareholdings_by_holder_id_last_four_and_name(company_profile_id, last_four, name) do
    holder_id_like = "%#{last_four}"

    query =
      from(sh in Shareholding,
        where:
          sh.company_profile_id == ^company_profile_id and like(sh.holder_id, ^holder_id_like) and
            sh.account_name == ^name,
        order_by: [asc: sh.id]
      )

    Repo.all(query)
  end

  def get_shareholdings_by_registry_holder_ids(company_profile_id, registry_holder_ids) do
    query =
      from(sh in Shareholding,
        where:
          sh.company_profile_id == ^company_profile_id and
            sh.registry_holder_id in ^registry_holder_ids,
        order_by: [asc: sh.id]
      )

    Repo.all(query)
  end

  # Get shareholdings by registry_holder_ids who have missing address_line_one
  def get_shareholdings_with_missing_address_by_registry_holder_ids(company_profile_id, registry_holder_ids) do
    Shareholding
    |> where(company_profile_id: ^company_profile_id)
    |> where([sh], is_nil(sh.address_line_one))
    |> where([sh], sh.registry_holder_id in ^registry_holder_ids)
    |> Repo.all()
  end

  # Check if there are still shareholdings with missing information within the company
  # For determining if still need to get previous report to populate shareholdings data
  def missing_shareholdings_information?(company_profile_id) do
    Shareholding
    |> where(company_profile_id: ^company_profile_id)
    |> where([sh], is_nil(sh.address_line_one))
    |> Repo.exists?()
  end

  @doc """
  Reconcile shareholder contact information with missing data.

  It will get new info comming from registry and try to update both contact and shareholder.

  A reconciliation is needed because several accounts can come back with same email address,
  so we might need to reconcile multiple email addresses for a single contact:

                   Contact 1
              (<EMAIL>)
                    /   \
                   /     \
                  /       \
        Shareholder 1   Shareholder 2

  If shareholder 2 has the same email address as shareholder 1, we need to reconcile them. so it means contact 2 will be removed.

  Returns the updated lookup map of shareholders with contact information, list of contacts to be updated and list of contacts to be deleted
  """
  def reconcile_shareholder_contact(new_info_lum, placeholders_with_contact, company_profile_id) do
    # Separate shareholders with/without emails in new_info_lum
    {no_email, with_email} =
      Enum.split_with(placeholders_with_contact, fn shareholder ->
        new_info_lum |> Map.get(shareholder.registry_holder_id) |> Map.get(:email) |> is_nil()
      end)

    # Extract all unique emails from new_info_lum
    emails =
      new_info_lum
      |> Enum.map(fn {_, %{email: email}} -> email end)
      |> Enum.reject(&is_nil/1)
      |> Enum.uniq()

    # Find existing contacts with these emails
    existing_contact_map =
      Contact
      |> where(company_profile_id: ^company_profile_id)
      |> where([c], c.email in ^emails)
      |> select([c], {c.email, c.id})
      |> Repo.all()
      |> Map.new()

    # Group shareholders by email and process each group
    {updated_lum, contacts_to_update, contacts_to_delete} =
      with_email
      |> Enum.group_by(fn sh -> new_info_lum |> Map.get(sh.registry_holder_id) |> Map.get(:email) end)
      |> Enum.reduce({new_info_lum, [], []}, fn {email, shareholders}, {acc_lum, acc_update, acc_delete} ->
        if Map.has_key?(existing_contact_map, email) do
          contact_id = existing_contact_map[email]
          to_delete = Enum.map(shareholders, & &1.contact.id)

          new_acc_lum =
            Enum.reduce(shareholders, acc_lum, fn sh, lum_acc ->
              updated_sh = lum_acc |> Map.get(sh.registry_holder_id) |> Map.put(:contact_id, contact_id)
              Map.put(lum_acc, sh.registry_holder_id, updated_sh)
            end)

          {new_acc_lum, acc_update, acc_delete ++ to_delete}
        else
          # Case 2 & 3: Handle either multiple shareholders with same email or single shareholder
          [first | rest] = shareholders
          contact_to_keep = first.contact
          to_delete = Enum.map(rest, & &1.contact.id)

          new_acc_lum =
            Enum.reduce(shareholders, acc_lum, fn sh, lum_acc ->
              updated_sh = lum_acc |> Map.get(sh.registry_holder_id) |> Map.put(:contact_id, contact_to_keep.id)
              Map.put(lum_acc, sh.registry_holder_id, updated_sh)
            end)

          {new_acc_lum,
           acc_update ++
             [%{id: contact_to_keep.id, email: email, company_profile_id: contact_to_keep.company_profile_id}],
           acc_delete ++ to_delete}
        end
      end)

    # The without email I just update the contact id, so everything will have a contact id in the end
    final_lum =
      Enum.reduce(no_email, updated_lum, fn shareholder, acc ->
        updated_sh = acc |> Map.get(shareholder.registry_holder_id) |> Map.put(:contact_id, shareholder.contact.id)
        Map.put(acc, shareholder.registry_holder_id, updated_sh)
      end)

    {final_lum, contacts_to_update, contacts_to_delete}
  end

  def get_shareholdings_by_company_profile_id(company_profile_id) do
    query =
      from(sh in Shareholding,
        where: sh.company_profile_id == ^company_profile_id,
        order_by: [asc: sh.id]
      )

    Repo.all(query)
  end

  def get_shareholding_registry_holder_ids(company_profile_id) do
    query =
      from(sh in Shareholding,
        where: sh.company_profile_id == ^company_profile_id,
        order_by: [asc: sh.id],
        select: sh.registry_holder_id
      )

    Repo.all(query)
  end

  def get_shareholding_registry_holder_ids_with_no_movements(company_profile_id, limit \\ 100) do
    # Didn't use the movement_count counter cache to ensure correct re-import

    # Use case:
    # Delete registers_share_movements and registers_daily_holdings to re-import registry data
    # Didn't delete registers_shareholdings to preserve their relationship with investors_users and contacts_contacts
    # If we didn't reset the movement_count counter cache to 0, this function will think all shareholders have movements
    # But they don't actually have any share movements, resulting in no movements get imported
    # Think re-import will be common in this stage

    Shareholding
    |> join(
      :left_lateral,
      [sh],
      sm in fragment(
        "SELECT id FROM registers_share_movements WHERE shareholding_id = ? ORDER BY id DESC LIMIT 1",
        sh.id
      ),
      on: true
    )
    |> where([sh], sh.company_profile_id == ^company_profile_id)
    |> where([_, sm], is_nil(sm.id))
    |> order_by([sh], asc: sh.id)
    |> limit(^limit)
    |> select([sh], sh.registry_holder_id)
    |> Repo.all()
  end

  # Currently only supports "new" activity type
  def get_shareholdings_for_campaign_channel_by_activity_type(%{
        company_profile_id: company_profile_id,
        channel: "mail",
        shareholder_activity_type: "new",
        start_date: start_date,
        end_date: end_date
      }) do
    shareholdings_activity_query =
      Gaia.Dashboard.shareholder_trading_activity_query(
        company_profile_id,
        start_date,
        end_date,
        "new",
        nil
      )

    if is_nil(shareholdings_activity_query) do
      []
    else
      Shareholding
      |> join(
        :inner,
        [sh],
        activity in subquery(shareholdings_activity_query),
        on: activity.shareholding_id == sh.id
      )
      |> where(
        [sh],
        sh.company_profile_id == ^company_profile_id and sh.share_count > 0 and
          not is_nil(sh.address_postcode) and sh.address_country == ^"AUSTRALIA"
      )
      |> Repo.all()
    end
  end

  def get_shareholdings_for_campaign_channel_by_activity_type(%{
        company_profile_id: company_profile_id,
        channel: "email",
        shareholder_activity_type: "new",
        start_date: start_date,
        end_date: end_date
      }) do
    channel_atom = String.to_atom("email")

    shareholdings_activity_query =
      Gaia.Dashboard.shareholder_trading_activity_query(
        company_profile_id,
        start_date,
        end_date,
        "new",
        nil
      )

    if is_nil(shareholdings_activity_query) do
      []
    else
      Shareholding
      |> join(:left, [sh], n in assoc(sh, :notification_preferences),
        on: n.invalidated == false and n.channel == ^channel_atom
      )
      |> join(
        :inner,
        [sh],
        activity in subquery(shareholdings_activity_query),
        on: activity.shareholding_id == sh.id
      )
      |> where(
        [sh, n],
        is_nil(n.id) and sh.company_profile_id == ^company_profile_id and sh.share_count > 0 and
          not is_nil(sh.email)
      )
      |> Repo.all()
    end
  end

  def get_shareholdings_for_campaign_channel_by_activity_type(_), do: []

  def get_shareholdings_for_campaign_channel(%{company_profile_id: company_profile_id, channel: "mail"}) do
    Shareholding
    |> where(
      [sh],
      sh.company_profile_id == ^company_profile_id and sh.share_count > 0 and
        not is_nil(sh.address_postcode) and sh.address_country == ^"AUSTRALIA"
    )
    |> Repo.all()
  end

  def get_shareholdings_for_campaign_channel(%{company_profile_id: company_profile_id, channel: "email"}) do
    channel_atom = String.to_atom("email")

    Shareholding
    |> join(:left, [sh], n in assoc(sh, :notification_preferences),
      on: n.invalidated == false and n.channel == ^channel_atom
    )
    |> where(
      [sh, n],
      is_nil(n.id) and sh.company_profile_id == ^company_profile_id and sh.share_count > 0 and
        not is_nil(sh.email)
    )
    |> Repo.all()
  end

  def get_shareholdings_for_campaign_channel(_), do: []

  def shareholdings_query_by_company_profile_id(options, id) do
    new_filters = [%{key: "company_profile_id", value: id} | Map.get(options, :filters, [])]

    options
    |> Map.put(:filters, new_filters)
    |> Enum.reduce(Shareholding, fn
      {:filters, filters}, query ->
        shareholdings_filter_with(query, filters)

      {:orders, orders}, query ->
        shareholdings_order_with(query, orders)

      _, query ->
        query
    end)
    |> distinct(true)
  end

  defp shareholdings_filter_with(query, filters) do
    Enum.reduce(filters, query, fn
      %{key: _, value: "undefined"}, query ->
        query

      %{key: _, value: "none"}, query ->
        query

      %{key: "company_profile_id", value: company_profile_id}, query ->
        where(query, [q], q.company_profile_id == ^company_profile_id)

      %{key: "has_email", value: "true"}, query ->
        query
        |> where([q], not is_nil(q.email))
        |> where([q], not fragment("TRIM(?) = ''", q.email))

      %{key: "broker_short_names", value: broker_short_names}, query ->
        filter_shareholdings_by_broker_short_name(query, broker_short_names)

      %{key: "broker_pids", value: broker_pids}, query ->
        where(query, [q], q.broker_pid in ^String.split(broker_pids, ","))

      %{key: "min_share_count", value: min_share_count}, query ->
        where(query, [q], q.share_count >= ^min_share_count)

      %{key: "max_share_count", value: max_share_count}, query ->
        where(query, [q], q.share_count <= ^max_share_count)

      %{key: "min_months_held", value: min_months_held}, query ->
        {num_of_months, _} = Integer.parse(min_months_held)

        where(
          query,
          [q],
          fragment(
            "? + interval '1 months' * ? <= DATE(now() AT TIME ZONE 'Australia/Sydney')",
            q.current_holding_start_date,
            ^num_of_months
          )
        )

      %{key: "max_months_held", value: max_months_held}, query ->
        {num_of_months, _} = Integer.parse(max_months_held)

        where(
          query,
          [q],
          fragment(
            "? + interval '1 months' * ? >= DATE(now() AT TIME ZONE 'Australia/Sydney')",
            q.current_holding_start_date,
            ^num_of_months
          )
        )

      %{key: "min_movement_count", value: min_movement_count}, query ->
        where(query, [q], q.movement_count >= ^min_movement_count)

      %{key: "max_movement_count", value: max_movement_count}, query ->
        where(query, [q], q.movement_count <= ^max_movement_count)

      %{key: "search", value: search}, query ->
        where(
          query,
          [q],
          ilike(fragment("? || ' ' || ?", coalesce(q.email, ""), q.account_name), ^"%#{search}%")
        )

      %{key: "include_past", value: "false"}, query ->
        where(query, [q], q.share_count > 0)

      %{key: "include_past", value: "true"}, query ->
        query

      %{key: "has_contact", value: "linked-only"}, query ->
        where(query, [q], not is_nil(q.contact_id))

      %{key: "has_contact", value: "unlinked-only"}, query ->
        where(query, [q], is_nil(q.contact_id))

      %{key: "traits", value: "top_20"}, query ->
        filter_shareholdings_by_share_count_rank(query, 20)

      %{key: "traits", value: "top_50"}, query ->
        filter_shareholdings_by_share_count_rank(query, 50)

      %{key: "traits", value: "spp"}, query ->
        query
        |> join(:inner, [q], sm in ShareMovement, on: sm.shareholding_id == q.id)
        |> where([q, sm], q.id in subquery(filter_shareholdings_by_spp_participation()))

      %{key: "traits", value: "placement"}, query ->
        query
        |> join(:inner, [q], p in Participant, on: p.shareholding_id == q.id)
        |> where([q, p], q.id in subquery(linked_placement_participants_count_query()))

      %{key: "traits", value: "hnw_behaviour"}, query ->
        where(query, [q], not is_nil(q.hnw_identified_at) and not is_nil(q.hnw_behaviour))

      %{key: "traits", value: _value}, query ->
        query

      %{key: "location", value: location}, query ->
        filter_shareholdings_by_location(query, location)

      %{key: "start_date", value: ""}, query ->
        query

      %{key: "start_date", value: date}, query ->
        from(q in query,
          where: q.current_holding_start_date >= ^NaiveDateTime.from_iso8601!(date)
        )

      %{key: "end_date", value: ""}, query ->
        query

      %{key: "end_date", value: date}, query ->
        from(q in query,
          where: q.current_holding_start_date <= ^NaiveDateTime.from_iso8601!(date)
        )
    end)
  end

  defp shareholdings_order_with(query, orders) do
    orders
    |> Enum.reduce(query, fn
      %{key: "id", value: "asc"}, query ->
        order_by(query, [q], asc: q.id)

      %{key: "id", value: "desc"}, query ->
        order_by(query, [q], desc: q.id)

      %{key: "current_holding_start_date", value: "asc"}, query ->
        order_by(query, [q], asc: q.current_holding_start_date)

      %{key: "current_holding_start_date", value: "desc"}, query ->
        order_by(query, [q], desc: q.current_holding_start_date)

      %{key: "movement_count", value: "asc"}, query ->
        order_by(query, [q], asc: q.movement_count)

      %{key: "movement_count", value: "desc"}, query ->
        order_by(query, [q], desc: q.movement_count)

      %{key: "share_count", value: "asc"}, query ->
        order_by(query, [q], asc_nulls_first: q.share_count)

      %{key: "share_count", value: "desc"}, query ->
        order_by(query, [q], desc_nulls_last: q.share_count)

      %{key: "account_name", value: "asc"}, query ->
        order_by(query, [q], asc: q.account_name)

      %{key: "account_name", value: "desc"}, query ->
        order_by(query, [q], desc: q.account_name)

      %{key: "contact_id", value: "asc"}, query ->
        order_by(query, [q], asc_nulls_first: q.contact_id)

      %{key: "contact_id", value: "desc"}, query ->
        order_by(query, [q], desc_nulls_last: q.contact_id)

      %{key: "contact_id", value: contact_id}, query ->
        query
        |> select_merge([q], %{
          current_contact_id:
            fragment(
              "CASE WHEN ? = ? THEN 0 ELSE 1 END",
              ^(contact_id |> Integer.parse() |> elem(0)),
              q.contact_id
            )
        })
        |> subquery()
        |> order_by(
          [q],
          asc: q.current_contact_id
        )

      %{key: "contact_email", value: "asc"}, query ->
        query
        |> join(:inner, [q], c in assoc(q, :contact))
        |> order_by([q, c], asc_nulls_first: c.email)
        |> select_merge([q, c], %{email: c.email})

      %{key: "contact_email", value: "desc"}, query ->
        query
        |> join(:inner, [q], c in assoc(q, :contact))
        |> order_by([q, c], desc_nulls_last: c.email)
        |> select_merge([q, c], %{email: c.email})
    end)
    # Add a default order_by id to resolve the ordering of equal values
    # Absinthe pagination requires the query result to be clearly ordered
    |> order_by([q], desc_nulls_last: q.inserted_at, desc: q.id)
  end

  defp filter_shareholdings_by_share_count_rank(query, max_rank) do
    query
    |> select(
      [q],
      q
    )
    |> select_merge(
      [q],
      %{
        rank_by_share_count: fragment("RANK() OVER (ORDER BY ? DESC NULLS LAST, ? DESC)", q.share_count, q.id)
      }
    )
    |> subquery()
    |> where([q], q.rank_by_share_count <= ^max_rank)
  end

  defp filter_shareholdings_by_spp_participation do
    from(sm in ShareMovement,
      where:
        fragment(
          "LOWER(REPLACE(?, ' ', '')) IN (?, ?)",
          sm.movement_type,
          "sharepurchaseplan",
          "spp"
        ),
      select: sm.shareholding_id
    )
  end

  defp linked_placement_participants_count_query do
    from(p in Participant,
      group_by: fragment("? having count(?) > 0", p.shareholding_id, p.shareholding_id),
      select: p.shareholding_id
    )
  end

  defp filter_shareholdings_by_location(query, location) do
    locations = location |> String.split(",") |> Enum.map(&String.upcase(&1))
    states = Enum.filter(locations, &(!Enum.member?(["NZ", "OTHER"], &1)))
    countries = Enum.filter(locations, &Enum.member?(["NZ", "OTHER"], &1))

    case locations do
      [""] ->
        query

      _ ->
        where(
          query,
          ^dynamic(
            [q],
            ^filter_shareholdings_where_states(states) or
              ^filter_shareholdings_where_countries(countries)
          )
        )
    end
  end

  defp filter_shareholdings_where_states([]), do: dynamic(false)

  defp filter_shareholdings_where_states(states), do: dynamic([q], q.address_state in ^states)

  defp filter_shareholdings_where_countries(countries) do
    Enum.reduce(countries, dynamic(false), fn
      "NZ", dynamic ->
        dynamic([q], ^dynamic or q.address_country == "NEW ZEALAND")

      "OTHER", dynamic ->
        dynamic([q], ^dynamic or q.address_country not in ["AUSTRALIA", "NEW ZEALAND"])

      _, dynamic ->
        dynamic
    end)
  end

  defp filter_shareholdings_by_broker_short_name(query, broker_short_names) do
    short_names = String.split(broker_short_names, ",")

    case short_names do
      [""] ->
        query

      _ ->
        broker_pids = broker_short_names_to_pids(short_names)

        where(query, [q], q.broker_pid in ^broker_pids)
    end
  end

  def broker_short_names_to_pids(short_names) do
    Enum.reduce(Brokers.list_brokers(), [], fn broker, acc ->
      if Enum.member?(short_names, broker.name_short) do
        Enum.uniq(broker.pids ++ acc)
      else
        acc
      end
    end)
  end

  @doc """
  Creates a shareholding.

  ## Examples

      iex> create_shareholding(%{field: value})
      {:ok, %Shareholding{}}

      iex> create_shareholding(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_shareholding(attrs \\ %{}) do
    %Shareholding{}
    |> Shareholding.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a shareholding.

  ## Examples

      iex> update_shareholding(shareholding, %{field: new_value})
      {:ok, %Shareholding{}}

      iex> update_shareholding(shareholding, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_shareholding(%Shareholding{} = shareholding, attrs) do
    shareholding
    |> Shareholding.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a shareholding.

  ## Examples

      iex> delete_shareholding(shareholding)
      {:ok, %Shareholding{}}

      iex> delete_shareholding(shareholding)
      {:error, %Ecto.Changeset{}}

  """
  def delete_shareholding(%Shareholding{} = shareholding) do
    Repo.delete(shareholding)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking shareholding changes.

  ## Examples

      iex> change_shareholding(shareholding)
      %Ecto.Changeset{data: %Shareholding{}}

  """
  def change_shareholding(%Shareholding{} = shareholding, attrs \\ %{}) do
    Shareholding.changeset(shareholding, attrs)
  end

  @doc """
  Returns `true` if all ids belong to the company.

  ## Examples

      iex> shareholding_ids_belong_to_company?([1, 2], 1)
      true

  """
  def shareholding_ids_belong_to_company?(ids, company_profile_id) do
    Shareholding
    |> where(company_profile_id: ^company_profile_id)
    |> where([sh], sh.id in ^ids)
    |> Repo.aggregate(:count)
    |> Kernel.==(Enum.count(ids))
  end

  def insert_formated_phone_number_into_contacts(company_profile_id) do
    Shareholding
    |> where(company_profile_id: ^company_profile_id)
    |> where([sh], not is_nil(sh.phone_number))
    |> preload(:contact)
    |> Repo.all()
    |> Enum.map(fn
      %Shareholding{phone_number: phone_number, contact: %Contact{} = contact} ->
        clean_up_number = clean_up_phone_number_format(phone_number)
        contact |> Contact.changeset(%{phone_number: clean_up_number}) |> Repo.update()

      _ ->
        :skip
    end)
  end

  # IF it is 04 or 03, change into +614(3)xx
  # IF it is 61 then add +
  # If it is just 4 or 3 then add +61
  def clean_up_phone_number_format(phone_number) do
    phone_number
    # Remove leading and trailing whitespaces
    |> String.replace(" ", "")
    |> case do
      # Case: Starts with "0x"
      <<?0, rest::binary>> -> "+61" <> rest
      # Case: Starts with "61"
      <<?6, ?1, rest::binary>> -> "+" <> rest
      # Case: Starts with "4"
      <<?4, rest::binary>> -> "+61" <> rest
      # Case: Starts with "3"
      <<?3, rest::binary>> -> "+61" <> rest
      # Case: No match, return as is
      _ -> phone_number
    end
  end

  @doc """
  Gets brokers_breakdown of a company

  Returns a map %{"broker_pid" => count}
  """
  def get_brokers_breakdown_by_company_profile_id(id) do
    Shareholding
    |> where([sh], sh.company_profile_id == ^id)
    |> where([sh], not is_nil(sh.broker_pid) and sh.broker_pid != "")
    |> group_by([sh], [sh.broker_pid])
    |> select([sh], {sh.broker_pid, count(sh)})
    |> Repo.all()
    |> Map.new()
  end

  @doc """
  Returns the number of shareholders and shares for brokers on a given date
  """
  def get_broker_breakdown(company_profile_id, %Date{} = date) do
    DailyHolding
    |> join(:inner, [dh], sh in assoc(dh, :shareholding))
    |> where([dh, sh], dh.date == ^date)
    |> where([dh, sh], sh.company_profile_id == ^company_profile_id)
    |> where([dh, sh], not is_nil(sh.broker_pid) and sh.broker_pid != "")
    |> group_by([dh, sh], sh.broker_pid)
    |> select([dh, sh], %{broker_pid: sh.broker_pid})
    |> select_merge([dh, sh], %{shareholders_count: count(dh)})
    |> select_merge([dh, sh], %{total_shares: type(sum(dh.balance), :integer)})
    |> Repo.all()
  end

  @doc """
  Returns the net movements for brokers
  """
  def get_broker_movements(company_profile_id, %Date{} = start_date, %Date{} = end_date) do
    # For net_movements, we include consolidations (rabbit hole).

    ShareMovement
    |> join(:inner, [sm], sh in assoc(sm, :shareholding))
    |> where([sm, sh], sh.company_profile_id == ^company_profile_id)
    |> where([sm, sh], not is_nil(sh.broker_pid) and sh.broker_pid != "")
    |> where([sm, sh], sm.settled_at >= ^start_date)
    |> where([sm, sh], sm.settled_at <= ^end_date)
    |> group_by([sm, sh], sh.broker_pid)
    |> select([sm, sh], %{
      broker_pid: sh.broker_pid,
      net_movements: type(sum(sm.movement), :integer)
    })
    |> Repo.all()
  end

  @doc ~S"""
  Inserts all shareholdings and create or link them to contact.

  Logic for creating or linking contact:
  - Split shareholder into 3 groups:
      1. Shareholders that need to be updated
        - Update existing shareholder, we don't update their contact
      2. Shareholders that have an email and we find an existing contact (Link them to existing contact)
        - We create the shareholder linking them to the existing contact
      3. Shareholders that either have no email or we can't find an existing contact (Create new contact and link them)
        - Create contact and link them to shareholdings we are about to create

  Rabbit holes (we decided to leave them for now):
  - Existing shareholders changed email address
  """
  def insert_all_shareholdings_and_link_to_contact(shareholdings, company_profile_id, opts \\ [])
      when is_list(shareholdings) do
    # Temp solution: Boardroom import failed because upserting with metadata could be very slow
    # Maybe we should drop metadata altogether and re-import instead
    update_shareholding_metadata = Keyword.get(opts, :update_shareholding_metadata, true)

    # Outside of transaction
    insert_date = NaiveDateTime.utc_now(:second)
    cleaned_shareholdings = Enum.map(shareholdings, &clean_shareholding/1)
    register_holder_ids = Enum.map(shareholdings, & &1.registry_holder_id)

    Ecto.Multi.new()
    |> Ecto.Multi.put(:insert_date, insert_date)
    |> Ecto.Multi.put(:cleaned_shareholdings, cleaned_shareholdings)
    |> Ecto.Multi.put(:register_holder_ids, register_holder_ids)
    |> Ecto.Multi.run(:latest_contact_id, fn _, _ ->
      # For the 1st rule, see above
      latest_contact_id =
        Contact
        |> where(company_profile_id: ^company_profile_id)
        |> Repo.aggregate(:max, :id)
        |> Kernel.||(0)

      {:ok, latest_contact_id}
    end)
    |> Ecto.Multi.run(:updatable_shareholders, fn _,
                                                  %{
                                                    register_holder_ids: register_holder_ids,
                                                    cleaned_shareholdings: cleaned_shareholdings
                                                  } ->
      # Linked shareholders that need to be updated
      shareholding_lum =
        Shareholding
        |> where(company_profile_id: ^company_profile_id)
        |> where([sh], sh.registry_holder_id in ^register_holder_ids)
        |> select([sh], {sh.registry_holder_id, sh.id})
        |> Repo.all()
        |> Map.new()

      updatable_shareholders =
        cleaned_shareholdings
        |> Enum.filter(&Map.has_key?(shareholding_lum, &1.registry_holder_id))
        |> Enum.map(&Map.put(&1, :id, shareholding_lum[&1.registry_holder_id]))

      {:ok, updatable_shareholders}
    end)
    |> Ecto.Multi.run(:linkable_shareholders, fn _,
                                                 %{
                                                   cleaned_shareholdings: cleaned_shareholdings,
                                                   updatable_shareholders: updatable_shareholders
                                                 } ->
      updatable_ids = Enum.map(updatable_shareholders, & &1.registry_holder_id)

      shareholders_with_email =
        cleaned_shareholdings
        |> Enum.reject(&is_nil(&1.email))
        |> Enum.filter(&(&1.registry_holder_id not in updatable_ids))

      emails = shareholders_with_email |> Enum.map(& &1.email) |> Enum.uniq()

      # Shareholders with email that need to be linked to a contact
      existing_contact_with_email =
        Contact
        |> where(company_profile_id: ^company_profile_id)
        |> where([c], c.email in ^emails)
        |> select([c], {c.email, c.id})
        |> Repo.all()
        |> Map.new()

      linkable_shareholders =
        shareholders_with_email
        |> Enum.reject(&(not Map.has_key?(existing_contact_with_email, &1.email)))
        |> Enum.map(fn shareholding ->
          shareholding
          |> Map.put(:contact_id, existing_contact_with_email[shareholding.email])
          |> Map.put(:inserted_at, {:placeholder, :now})
          |> Map.put(:updated_at, {:placeholder, :now})
          |> Map.put(:contact_linked_at, {:placeholder, :now})
        end)

      {:ok, linkable_shareholders}
    end)
    |> Ecto.Multi.run(:creatable_shareholders, fn _,
                                                  %{
                                                    cleaned_shareholdings: cleaned_shareholdings,
                                                    linkable_shareholders: linkable_shareholders,
                                                    updatable_shareholders: updatable_shareholders
                                                  } ->
      updatable_ids = Enum.map(updatable_shareholders, & &1.registry_holder_id)
      linkable_ids = Enum.map(linkable_shareholders, & &1.registry_holder_id)

      # Everything that did not link to a contact or were not a pre-existing shareholding
      creatable_shareholders =
        Enum.filter(
          cleaned_shareholdings,
          &(&1.registry_holder_id not in updatable_ids and
              &1.registry_holder_id not in linkable_ids)
        )

      {:ok, creatable_shareholders}
    end)
    |> Ecto.Multi.merge(fn %{
                             updatable_shareholders: updatable_shareholders,
                             linkable_shareholders: linkable_shareholders,
                             insert_date: insert_date
                           } ->
      # These shareholders have a contacts to be linked to, so I just upsert them
      (updatable_shareholders ++ linkable_shareholders)
      |> Enum.chunk_every(2_000)
      |> Enum.reduce(Ecto.Multi.new(), fn shareholdings_chunk, multi_acc ->
        registry_holder_id =
          shareholdings_chunk
          |> List.first()
          |> Map.get(:registry_holder_id)
          |> String.downcase()

        fields_to_replace = [
          :account_name,
          :address_line_one,
          :address_line_two,
          :address_city,
          :address_state,
          :address_country,
          :address_postcode,
          :crest_id,
          :email,
          :holder_id,
          :phone_number,
          :updated_at
        ]

        fields_to_replace =
          if update_shareholding_metadata do
            [:metadata | fields_to_replace]
          else
            fields_to_replace
          end

        # this will meanly update the shareholding with the same registry_holder_id or create a new one if not existing
        Ecto.Multi.insert_all(
          multi_acc,
          {:shareholder_update, registry_holder_id},
          Shareholding,
          shareholdings_chunk,
          on_conflict: {:replace, fields_to_replace},
          conflict_target: [:company_profile_id, :registry_holder_id],
          placeholders: %{now: insert_date}
        )
      end)
    end)
    |> Ecto.Multi.merge(fn %{insert_date: insert_date, creatable_shareholders: creatable_shareholders} ->
      {no_email, with_email} = Enum.split_with(creatable_shareholders, &is_nil(&1.email))

      contacts_with_emails =
        with_email
        |> Enum.group_by(& &1.email)
        |> Enum.map(fn {email, shareholdings} ->
          # Default to the first shareholding's account_name for the contact name
          first_shareholding = List.first(shareholdings)
          first_name = extract_contact_first_name(first_shareholding.account_name)

          %{
            company_profile_id: company_profile_id,
            email: email,
            first_name: first_name,
            registry_holder_ids: Enum.map(shareholdings, & &1.registry_holder_id),
            inserted_at: {:placeholder, :now},
            updated_at: {:placeholder, :now},
            contact_source: :registry_import
          }
        end)

      contacts_without_email =
        Enum.map(no_email, fn %{registry_holder_id: registry_holder_id, account_name: account_name} ->
          first_name = extract_contact_first_name(account_name)

          %{
            company_profile_id: company_profile_id,
            email: nil,
            first_name: first_name,
            registry_holder_ids: [registry_holder_id],
            inserted_at: {:placeholder, :now},
            updated_at: {:placeholder, :now},
            contact_source: :registry_import
          }
        end)

      (contacts_with_emails ++ contacts_without_email)
      |> Enum.chunk_every(2_000)
      |> Enum.reduce(Ecto.Multi.new(), fn contacts, multi_acc ->
        holder_id =
          contacts
          |> List.first()
          |> Map.get(:registry_holder_ids)
          |> List.first()
          |> String.downcase()

        Ecto.Multi.insert_all(
          multi_acc,
          {:create_contacts, holder_id},
          Contact,
          contacts,
          placeholders: %{now: insert_date}
        )
      end)
    end)
    |> Ecto.Multi.run(:created_contacts, fn _,
                                            %{
                                              latest_contact_id: latest_contact_id,
                                              creatable_shareholders: creatable_shareholders
                                            } ->
      holder_ids = Enum.map(creatable_shareholders, & &1.registry_holder_id)

      inserted_contacts =
        Contact
        |> where(company_profile_id: ^company_profile_id)
        |> where([c], c.id > ^latest_contact_id)
        |> where([c], fragment("? && ?", ^holder_ids, c.registry_holder_ids))
        |> Repo.all()

      {:ok, inserted_contacts}
    end)
    |> Ecto.Multi.merge(fn %{
                             insert_date: insert_date,
                             created_contacts: inserted_contacts,
                             creatable_shareholders: creatable_shareholders
                           } ->
      contacts_lum =
        inserted_contacts
        |> Enum.flat_map(fn contact ->
          Enum.map(contact.registry_holder_ids, &{&1, contact.id})
        end)
        |> Map.new()

      creatable_shareholders
      |> Enum.map(fn shareholding ->
        contact_id = contacts_lum[shareholding.registry_holder_id]

        shareholding
        |> Map.put(:contact_id, contact_id)
        |> Map.put(:inserted_at, {:placeholder, :now})
        |> Map.put(:updated_at, {:placeholder, :now})
      end)
      |> Enum.chunk_every(2_000)
      |> Enum.reduce(Ecto.Multi.new(), fn shareholdings_chunk, multi_acc ->
        registry_holder_id =
          shareholdings_chunk
          |> List.first()
          |> Map.get(:registry_holder_id)
          |> String.downcase()

        Ecto.Multi.insert_all(
          multi_acc,
          {:shareholder_link, registry_holder_id},
          Shareholding,
          shareholdings_chunk,
          placeholders: %{now: insert_date}
        )
      end)
    end)
    |> Ecto.Multi.run(:post_process, fn _,
                                        %{
                                          insert_date: insert_date,
                                          created_contacts: inserted_contacts,
                                          linkable_shareholders: linkable_shareholders
                                        } ->
      bulk_unsubscribe_new_shareholding_contacts(inserted_contacts, company_profile_id)

      contact_ids = Enum.map(linkable_shareholders, & &1.contact_id)

      {_number_updated, updated_contacts} =
        Contact
        |> where([c], c.company_profile_id == ^company_profile_id and c.id in ^contact_ids)
        |> update([c],
          set: [
            lead_converted_at:
              fragment(
                "CASE WHEN ? THEN ? ELSE ? END",
                is_nil(c.lead_converted_at) and not is_nil(c.lead_identified_at),
                type(^insert_date, :naive_datetime),
                nil
              )
          ]
        )
        |> Repo.update_all([], returning: true)

      pending_welcome_contacts =
        List.wrap(inserted_contacts) ++ List.wrap(updated_contacts)

      pending_welcome_ids =
        pending_welcome_contacts
        |> Enum.filter(&(not is_nil(&1.email)))
        |> Enum.map(& &1.id)

      enqueue_welcome_emails(pending_welcome_ids, company_profile_id)

      {:ok, pending_welcome_ids}
    end)
    |> Repo.transaction(timeout: 600_000)
  end

  def enqueue_welcome_emails([], _), do: :skip

  def enqueue_welcome_emails(pending_welcome_ids, company_profile_id) do
    Gaia.Jobs.EnqueueWelcomeEmails.enqueue(
      %{
        "contact_ids" => pending_welcome_ids,
        "company_profile_id" => company_profile_id
      },
      schedule_in: {10_000_000_000, :minutes}
    )
  end

  # Clean shareholding before inserting them all
  def clean_shareholding(%{} = shareholding) do
    shareholding
    |> clean_shareholding_field(:email)
    |> clean_shareholding_field(:phone_number)
  end

  # Clean shareholding before inserting them all
  def clean_shareholding(nil), do: nil

  def clean_shareholding_field(%{} = shareholding, field) do
    shareholding
    |> Map.get(field)
    |> case do
      nil ->
        # Returns shareholding without any changes
        shareholding

      value when is_binary(value) ->
        value
        |> String.trim()
        |> String.downcase()
        |> case do
          "" ->
            # Replace the value of the field with nil
            Map.put(shareholding, field, nil)

          formatted_value ->
            # Replace the value of the field with cleaned value
            Map.put(shareholding, field, formatted_value)
        end

      value ->
        Map.put(shareholding, field, value)
    end
  end

  @doc """
  Extract first name from account name for contact creation.
  For now, puts the entire account_name into first_name as requested.
  Later this can be enhanced to split person vs organization names.
  """
  def extract_contact_first_name(account_name) when is_binary(account_name) do
    account_name
    |> String.trim()
    |> case do
      "" -> nil
      trimmed_name -> trimmed_name
    end
  end

  def extract_contact_first_name(_), do: nil

  # The placeholder shareholding is shareholding with only registry_holder_id and account_name
  def insert_placeholder_shareholdings(shareholdings) when is_list(shareholdings) do
    shareholdings
    |> Enum.chunk_every(4_000)
    |> Enum.reduce(Ecto.Multi.new(), fn shareholdings_chunk, multi_acc ->
      registry_holder_id =
        shareholdings_chunk
        |> List.first()
        |> Map.get(:registry_holder_id)
        |> String.downcase()

      atom_id = String.to_atom("insert_placeholder_shareholdings_#{registry_holder_id}")

      Ecto.Multi.insert_all(multi_acc, atom_id, Shareholding, shareholdings_chunk,
        on_conflict: :nothing,
        conflict_target: [:company_profile_id, :registry_holder_id]
      )
    end)
    |> Repo.transaction(timeout: 120_000)
  end

  def insert_placeholder_contacts([_ | _] = shareholdings) do
    {:ok, %{created_contacts: contacts_lum}} =
      shareholdings
      |> Enum.map(fn shareholder ->
        first_name = extract_contact_first_name(shareholder.account_name)

        %{
          company_profile_id: shareholder.company_profile_id,
          email: nil,
          first_name: first_name,
          registry_holder_ids: [shareholder.registry_holder_id],
          inserted_at: shareholder.inserted_at,
          updated_at: shareholder.updated_at,
          contact_source: :registry_import
        }
      end)
      |> Enum.chunk_every(2_000)
      |> Enum.reduce(Ecto.Multi.new(), fn contacts, multi_acc ->
        holder_id =
          contacts
          |> List.first()
          |> Map.get(:registry_holder_ids)
          |> List.first()
          |> String.downcase()

        Ecto.Multi.insert_all(
          multi_acc,
          {:placeholder_contacts, holder_id},
          Contact,
          contacts
        )
      end)
      |> Ecto.Multi.run(:created_contacts, fn _, _ ->
        holder_ids = Enum.map(shareholdings, & &1.registry_holder_id)
        company_profile_id = shareholdings |> List.first() |> Map.get(:company_profile_id)

        inserted_contacts =
          Contact
          |> where(company_profile_id: ^company_profile_id)
          |> where([c], fragment("? && ?", ^holder_ids, c.registry_holder_ids))
          |> Repo.all()

        contacts_lum =
          inserted_contacts
          |> Enum.flat_map(fn contact ->
            Enum.map(contact.registry_holder_ids, &{&1, contact.id})
          end)
          |> Map.new()

        {:ok, contacts_lum}
      end)
      |> Repo.transaction(timeout: 120_000)

    Enum.map(shareholdings, fn shareholding ->
      contact_id = contacts_lum[shareholding.registry_holder_id]

      Map.put(shareholding, :contact_id, contact_id)
    end)
  end

  def insert_placeholder_contacts(_), do: []

  @doc """
  Calculate and update the counter caches on shareholdings table
  """
  def update_shareholdings_counter_cache(company_profile_id, skip \\ []) do
    Ecto.Multi.new()
    |> Ecto.Multi.run(:reset_counter_cache, fn _, _ ->
      {:ok,
       if(Enum.member?(skip, :update_broker_pid),
         do: reset_shareholdings_counter_cache(company_profile_id, :skip_broker_pid),
         else: reset_shareholdings_counter_cache(company_profile_id)
       )}
    end)
    |> Ecto.Multi.run(:update_share_count, fn _, _ ->
      # We might use share_count to calculate other counter cache
      {:ok, update_shareholdings_share_count(company_profile_id)}
    end)
    |> Ecto.Multi.run(:update_biggest_movement, fn _, _ ->
      {:ok, update_shareholdings_biggest_movement(company_profile_id)}
    end)
    |> Ecto.Multi.run(:update_broker_pid, fn _, _ ->
      {:ok,
       if(Enum.member?(skip, :update_broker_pid),
         do: 0,
         else: update_shareholdings_broker_pid(company_profile_id)
       )}
    end)
    |> Ecto.Multi.run(:update_current_holding_start_date, fn _, _ ->
      {:ok, update_shareholdings_current_holding_start_date(company_profile_id)}
    end)
    |> Ecto.Multi.run(:update_initial_purchase_date, fn _, _ ->
      {:ok, update_shareholdings_initial_purchase_date(company_profile_id)}
    end)
    |> Ecto.Multi.run(:update_movement_count, fn _, _ ->
      {:ok, update_shareholdings_movement_count(company_profile_id)}
    end)
    |> Ecto.Multi.run(:update_estimated_profit_and_loss, fn _, _ ->
      # Needs to be run after updating share_count counter cache
      # We need the value of share_count to calculate estimated_profit_loss, estimated_total_purchase_value, estimated_total_sale_value
      {:ok, update_shareholdings_estimated_profit_purchases_and_sales(company_profile_id)}
    end)
    |> Ecto.Multi.run(:send_welcome_email, fn _, _ ->
      Gaia.Jobs.EnqueueWelcomeEmails.wake_up_job(company_profile_id)
      {:ok, nil}
    end)
    |> Repo.transaction(timeout: 600_000)
  end

  @doc """
  Reset the counter caches on shareholdings table
  """
  def reset_shareholdings_counter_cache(company_profile_id) do
    Shareholding
    |> where([sh], sh.company_profile_id == ^company_profile_id)
    |> update([sh],
      set: [
        biggest_movement: 0,
        broker_pid: nil,
        current_holding_start_date: nil,
        estimated_profit_loss: 0.0,
        initial_purchase_date: nil,
        movement_count: 0,
        share_count: 0
      ]
    )
    |> Repo.update_all([])
  end

  def reset_shareholdings_counter_cache(company_profile_id, :skip_broker_pid) do
    Shareholding
    |> where([sh], sh.company_profile_id == ^company_profile_id)
    |> update([sh],
      set: [
        biggest_movement: 0,
        current_holding_start_date: nil,
        estimated_profit_loss: 0.0,
        initial_purchase_date: nil,
        movement_count: 0,
        share_count: 0
      ]
    )
    |> Repo.update_all([])
  end

  @doc """
  Calculate and update shareholdings biggest movement
  """
  def update_shareholdings_biggest_movement(company_profile_id) do
    # Exclude consolidations from biggest movement
    biggest_movement_query =
      ShareMovement
      |> join(:inner, [sm], sh in Shareholding, on: sh.id == sm.shareholding_id)
      |> where([sm, sh], sh.company_profile_id == ^company_profile_id)
      |> where(
        [sm],
        fragment(
          "? NOT ILIKE ALL(ARRAY['%consolidation%', '%reconstruction%'])",
          sm.movement_type
        )
      )
      |> group_by([sm], sm.shareholding_id)
      |> select([sm], %{
        shareholding_id: sm.shareholding_id,
        biggest_movement: fragment("MAX(ABS(?))", sm.movement)
      })

    Shareholding
    |> join(:inner, [sh], sm in subquery(biggest_movement_query), on: sm.shareholding_id == sh.id)
    |> update([sh, sm], set: [biggest_movement: sm.biggest_movement])
    |> Repo.update_all([])
  end

  @doc """
  Find and update shareholdings broker_pid
  """
  def update_shareholdings_broker_pid(company_profile_id) do
    Companies.RegistryCredential
    |> where(company_profile_id: ^company_profile_id)
    |> Repo.one()
    |> case do
      %{service: :boardroom} ->
        # Update broker_pid from metadata
        Shareholding
        |> where(company_profile_id: ^company_profile_id)
        |> update([sh],
          set: [
            broker_pid: fragment("TRIM(LTRIM(?->'holdings'->0->>'brokerId', '0'))", sh.metadata)
          ]
        )
        |> Repo.update_all([])

      _ ->
        # For file upload, automic, computershare
        # Update broker_pid from movements
        broker_pid_query =
          Shareholding
          |> join(
            :left_lateral,
            [sh],
            sm in fragment(
              "SELECT broker_pid FROM registers_share_movements WHERE shareholding_id = ? AND NOT broker_pid IS NULL ORDER BY settled_at DESC, id DESC LIMIT 1",
              sh.id
            ),
            on: true
          )
          |> where([sh], sh.company_profile_id == ^company_profile_id)
          |> select([sh, sm], %{shareholding_id: sh.id, broker_pid: sm.broker_pid})

        Shareholding
        |> join(:inner, [sh], sm in subquery(broker_pid_query), on: sm.shareholding_id == sh.id)
        |> update([sh, sm], set: [broker_pid: sm.broker_pid])
        |> Repo.update_all([])
    end
  end

  @doc """
  Calculate and update shareholdings current holding start date
  """
  def update_shareholdings_current_holding_start_date(company_profile_id) do
    # We only need to consider current shareholders
    shareholdings_query =
      Shareholding
      |> where([sh], sh.company_profile_id == ^company_profile_id)
      |> where([sh], sh.share_count > 0)
      |> select([sh], %{id: sh.id, current_holding_start_date: sh.current_holding_start_date})
      |> subquery()

    # Get the first buy in date for all current shareholders since their latest sold out
    # Even if they sell all and buy some back the same day, we'll still consider them as holding that day
    # Consolidations should not be considered as sold out
    has_sold_out_query =
      shareholdings_query
      |> join(:inner, [sh], sm in ShareMovement, on: sm.shareholding_id == sh.id)
      |> order_by([sh, sm], asc: sh.id, asc: sm.settled_at)
      |> distinct(true)
      |> select([sh, sm], %{shareholding_id: sh.id, settled_at: sm.settled_at})
      |> subquery()
      |> join(
        :inner_lateral,
        [q],
        sm in fragment(
          "SELECT closing_balance FROM registers_share_movements WHERE shareholding_id = ? AND settled_at = ? AND movement_type NOT ILIKE ALL(ARRAY['%consolidation%', '%reconstruction%']) ORDER BY id DESC LIMIT 1",
          q.shareholding_id,
          q.settled_at
        ),
        on: true
      )
      |> where([q, sm], sm.closing_balance == 0)
      |> group_by([q, sm], q.shareholding_id)
      |> order_by([q, sm], asc: q.shareholding_id)
      |> select([q, sm], %{shareholding_id: q.shareholding_id, settled_at: max(q.settled_at)})
      |> subquery()
      |> join(:inner, [q], sm in ShareMovement,
        on: sm.shareholding_id == q.shareholding_id and sm.settled_at > q.settled_at
      )
      |> where([q, sm], sm.closing_balance > 0)
      |> group_by([q, sm], q.shareholding_id)
      |> order_by([q, sm], asc: q.shareholding_id)
      |> select([q, sm], %{shareholding_id: q.shareholding_id, date: min(sm.settled_at)})

    update_has_sold_out_query =
      Shareholding
      |> join(:inner, [sh], sm in subquery(has_sold_out_query), on: sm.shareholding_id == sh.id)
      |> update([sh, sm], set: [current_holding_start_date: sm.date])

    # For current shareholders who have never sold out, get their earliest buy in date
    # We know they have never sold out if their current_holding_start_date is still NULL
    never_sold_out_query =
      shareholdings_query
      |> join(:inner, [sh], sm in ShareMovement, on: sm.shareholding_id == sh.id)
      |> where([sh], is_nil(sh.current_holding_start_date))
      # |> where([sh, sm], sm.movement > 0) # We can't guarantee we will have the all time share movements due to csv upload
      |> group_by([sh], sh.id)
      |> select([sh, sm], %{shareholding_id: sh.id, date: min(sm.settled_at)})

    update_never_sold_out_query =
      Shareholding
      |> join(:inner, [sh], sm in subquery(never_sold_out_query), on: sm.shareholding_id == sh.id)
      |> update([sh, sm], set: [current_holding_start_date: sm.date])

    # When we have incomplete share movements due to csv upload
    # Some current shareholdings might still not have current_holding_start_date
    # Set to their initial purchase date
    update_to_initial_purchase_date_query =
      Shareholding
      |> join(:inner, [sh], shs in ^shareholdings_query, on: shs.id == sh.id)
      |> where([sh], is_nil(sh.current_holding_start_date))
      |> update([sh], set: [current_holding_start_date: sh.initial_purchase_date])

    Ecto.Multi.new()
    |> Ecto.Multi.update_all(:update_has_sold_out, update_has_sold_out_query, [])
    |> Ecto.Multi.update_all(:update_never_sold_out, update_never_sold_out_query, [])
    |> Ecto.Multi.update_all(
      :update_to_initial_purchase_date,
      update_to_initial_purchase_date_query,
      []
    )
    |> Repo.transaction(timeout: 300_000)
  end

  @doc """
  Calculate and update shareholdings estimated profit loss, estimated purchase and sale totals
  """
  def update_shareholdings_estimated_profit_purchases_and_sales(company_profile_id) do
    last_close_price =
      company_profile_id
      |> Markets.get_latest_timeseries_non_adjusted_by_company_profile_id()
      |> Map.get(:close)

    # Exclude consolidations from profit loss estimation
    estimated_profit_loss_query =
      Shareholding
      |> join(:inner, [sh], sm in ShareMovement, on: sm.shareholding_id == sh.id)
      |> where([sh], sh.company_profile_id == ^company_profile_id)
      |> where(
        [sh, sm],
        fragment(
          "? NOT ILIKE ALL(ARRAY['%consolidation%', '%reconstruction%'])",
          sm.movement_type
        )
      )
      |> group_by([sh], sh.id)
      |> select([sh, sm], %{
        id: sh.id,
        estimated_profit_loss:
          fragment(
            "ROUND((SUM(? * -?) + (?::double precision * ?))::NUMERIC, 2)",
            sm.movement,
            sm.estimated_price,
            sh.share_count,
            ^last_close_price
          ),
        estimated_total_purchase_value:
          fragment(
            "ROUND((SUM((CASE WHEN ? > 0 THEN ? ELSE 0 END) * ?))::NUMERIC, 2)",
            sm.movement,
            sm.movement,
            sm.estimated_price
          ),
        estimated_total_sale_value:
          fragment(
            "ROUND((ABS(SUM((CASE WHEN ? < 0 THEN ? ELSE 0 END) * ?)))::NUMERIC, 2)",
            sm.movement,
            sm.movement,
            sm.estimated_price
          )
      })

    Shareholding
    |> join(:inner, [sh], pf in subquery(estimated_profit_loss_query), on: pf.id == sh.id)
    |> update([sh, pf],
      set: [
        estimated_total_purchase_value: pf.estimated_total_purchase_value,
        estimated_total_sale_value: pf.estimated_total_sale_value,
        estimated_profit_loss: pf.estimated_profit_loss
      ]
    )
    |> Repo.update_all([])
  end

  # Calculate and update shareholdings initial purchase date
  def update_shareholdings_initial_purchase_date(company_profile_id) do
    initial_purchase_date_query =
      ShareMovement
      |> join(:inner, [sm], sh in Shareholding, on: sh.id == sm.shareholding_id)
      # |> where([sm], sm.movement > 0) # We can't guarantee we will have the all time share movements due to csv upload
      |> where([sm, sh], sh.company_profile_id == ^company_profile_id)
      |> group_by([sm], sm.shareholding_id)
      |> select([sm], %{
        shareholding_id: sm.shareholding_id,
        initial_purchase_date: fragment("DATE(MIN(?))", sm.settled_at)
      })

    Shareholding
    |> join(:inner, [sh], sm in subquery(initial_purchase_date_query), on: sm.shareholding_id == sh.id)
    |> update([sh, sm], set: [initial_purchase_date: sm.initial_purchase_date])
    |> Repo.update_all([])
  end

  @doc """
  Calculate and update shareholdings movement count
  """
  def update_shareholdings_movement_count(company_profile_id) do
    # Exclude consolidations from movement count
    movement_count_query =
      ShareMovement
      |> join(:inner, [sm], sh in Shareholding, on: sh.id == sm.shareholding_id)
      |> where([sm, sh], sh.company_profile_id == ^company_profile_id)
      |> where(
        [sm],
        fragment(
          "? NOT ILIKE ALL(ARRAY['%consolidation%', '%reconstruction%'])",
          sm.movement_type
        )
      )
      |> group_by([sm], sm.shareholding_id)
      |> select([sm], %{shareholding_id: sm.shareholding_id, movement_count: count(sm.id)})

    Shareholding
    |> join(:inner, [sh], sm in subquery(movement_count_query), on: sm.shareholding_id == sh.id)
    |> update([sh, sm], set: [movement_count: sm.movement_count])
    |> Repo.update_all([])
  end

  @doc """
  Calculate and update shareholdings share count
  """
  def update_shareholdings_share_count(company_profile_id) do
    share_count_query =
      ShareMovement
      |> where([sm], sm.company_profile_id == ^company_profile_id)
      |> group_by([sm], sm.shareholding_id)
      |> select([sm], %{shareholding_id: sm.shareholding_id, share_count: sum(sm.movement)})

    Shareholding
    |> join(:inner, [sh], sc in subquery(share_count_query), on: sc.shareholding_id == sh.id)
    |> update([sh, sc], set: [share_count: sc.share_count])
    |> Repo.update_all([])
  end

  @doc """
  Retrieve mapping for registry_holder_id to shareholding

  Returns %{"S00123456790" => %{id: 1, share_count: 0}
  """
  def get_registry_holder_id_to_shareholding_mapping(company_profile_id) do
    Shareholding
    |> join(
      :left_lateral,
      [sh],
      sm in fragment(
        "SELECT closing_balance FROM registers_share_movements WHERE shareholding_id = ? ORDER BY settled_at DESC, id DESC LIMIT 1",
        sh.id
      ),
      on: true
    )
    |> where([sh], sh.company_profile_id == ^company_profile_id)
    |> select([sh, sm], %{
      id: sh.id,
      company_profile_id: sh.company_profile_id,
      registry_holder_id: sh.registry_holder_id,
      share_count: coalesce(sm.closing_balance, 0)
    })
    |> Repo.all()
    |> Enum.reduce(%{}, &Map.put(&2, &1.registry_holder_id, &1))
  end

  @doc """
  Retrieve mapping for registry_holder_id to shareholding (specific to computershare)

  Returns %{"S00123456790" => %{id: 1, registry_holder_id: X000001234567, broker_pid: 1234, metadata: %{}}
  """
  def get_registry_holder_id_to_shareholding_mapping(company_profile_id, :computershare) do
    Shareholding
    |> where(company_profile_id: ^company_profile_id)
    |> select([sh], %{
      id: sh.id,
      company_profile_id: sh.company_profile_id,
      registry_holder_id: sh.registry_holder_id,
      broker_pid: sh.broker_pid,
      metadata: sh.metadata
    })
    |> Repo.all()
    |> Enum.reduce(%{}, &Map.put(&2, &1.registry_holder_id, &1))
  end

  def find_or_create_shareholding(
        %{registry_holder_id: registry_holder_id, company_profile_id: company_profile_id} = attrs
      ) do
    case get_shareholding_by(%{
           registry_holder_id: registry_holder_id,
           company_profile_id: company_profile_id
         }) do
      nil ->
        create_shareholding(attrs)

      %Shareholding{} = shareholding ->
        {:ok, shareholding}
    end
  end

  @doc """
  Returns the list of daily_holding.

  ## Examples

      iex> list_daily_holding()
      [%DailyHolding{}, ...]

  """
  def list_daily_holdings do
    Repo.all(DailyHolding)
  end

  @doc """
  Gets a single daily_holding.

  Raises `Ecto.NoResultsError` if the Daily holdings does not exist.

  ## Examples

      iex> get_daily_holding!(123)
      %DailyHolding{}

      iex> get_daily_holding!(456)
      ** (Ecto.NoResultsError)

  """
  def get_daily_holding!(id), do: Repo.get!(DailyHolding, id)

  @doc """
  Gets all daily_holdings for a shareholding within the given date range
  """
  def get_daily_holdings_by_shareholding_id(id, company_profile_id, %Date{} = start_date, %Date{} = end_date) do
    DailyHolding
    |> join(:inner, [dh], sh in Shareholding, on: sh.id == dh.shareholding_id and sh.id == ^id)
    |> where([dh, sh], sh.company_profile_id == ^company_profile_id)
    |> where([dh], dh.date >= ^start_date)
    |> where([dh], dh.date <= ^end_date)
    |> order_by([dh], asc: dh.date, asc: dh.id)
    |> Repo.all()
  end

  @doc """
  Creates a daily_holding.

  ## Examples

      iex> create_daily_holding(%{field: value})
      {:ok, %DailyHolding{}}

      iex> create_daily_holding(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_daily_holding(attrs \\ %{}) do
    %DailyHolding{}
    |> DailyHolding.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a daily_holding.

  ## Examples

      iex> update_daily_holding(daily_holding, %{field: new_value})
      {:ok, %DailyHolding{}}

      iex> update_daily_holding(daily_holding, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_daily_holding(%DailyHolding{} = daily_holding, attrs) do
    daily_holding
    |> DailyHolding.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a daily_holding.

  ## Examples

      iex> delete_daily_holding(daily_holding)
      {:ok, %DailyHolding{}}

      iex> delete_daily_holding(daily_holding)
      {:error, %Ecto.Changeset{}}

  """
  def delete_daily_holding(%DailyHolding{} = daily_holding) do
    Repo.delete(daily_holding)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking daily_holding changes.

  ## Examples

      iex> change_daily_holding(daily_holding)
      %Ecto.Changeset{data: %DailyHolding{}}

  """
  def change_daily_holding(%DailyHolding{} = daily_holding, attrs \\ %{}) do
    DailyHolding.changeset(daily_holding, attrs)
  end

  @doc """
  Insert daily holdings in bulk using a transaction to ensure atomicity
  """
  def insert_all_daily_holdings(daily_holdings) when is_list(daily_holdings) do
    # Need to use chunk_every because insert_all has a limit of 65535 parameters

    daily_holdings
    |> Enum.chunk_every(5_000)
    |> Enum.reduce(Ecto.Multi.new(), fn daily_holdings_chunk, multi_acc ->
      shareholding_id =
        daily_holdings_chunk
        |> List.first()
        |> Map.get(:shareholding_id)
        |> to_string()

      atom_id = String.to_atom("insert_daily_holdings_for_shareholding_#{shareholding_id}")

      Ecto.Multi.insert_all(multi_acc, atom_id, DailyHolding, daily_holdings_chunk)
    end)
    |> Repo.transaction(timeout: 120_000)
  end

  def update_daily_holdings_counter_cache(company_profile_id, %Date{} = date) do
    # We only need to consider shareholders on a particular day
    shareholdings_query =
      Shareholding
      |> join(:inner, [sh], dh in DailyHolding, on: dh.shareholding_id == sh.id)
      |> where([sh], sh.company_profile_id == ^company_profile_id)
      |> where([sh, dh], dh.date == ^date)
      |> select([sh, dh], %{
        id: sh.id,
        date: dh.date,
        initial_purchase_date: sh.initial_purchase_date
      })

    # Reset daily_holdings counter cache
    reset_counter_cache_query =
      DailyHolding
      |> join(:inner, [dh], sh in subquery(shareholdings_query), on: sh.id == dh.shareholding_id and sh.date == dh.date)
      |> update([], set: [current_holding_start_date: nil])

    # Get the first buy in date for shareholders since their latest sold out
    # Even if they sell all and buy some back the same day, we'll still consider them as holding that day
    # Consolidations should not be considered as sold out
    # Should only consider all movements less than equal to provided date
    has_sold_out_query =
      shareholdings_query
      |> subquery()
      |> join(:inner, [sh], sm in ShareMovement, on: sm.shareholding_id == sh.id and sm.settled_at <= sh.date)
      |> order_by([sh, sm], asc: sh.id, asc: sm.settled_at)
      |> distinct(true)
      |> select([sh, sm], %{shareholding_id: sh.id, settled_at: sm.settled_at})
      |> subquery()
      |> join(
        :inner_lateral,
        [q],
        sm in fragment(
          "SELECT closing_balance FROM registers_share_movements WHERE shareholding_id = ? AND settled_at = ? AND movement_type NOT ILIKE ALL(ARRAY['%consolidation%', '%reconstruction%']) ORDER BY id DESC LIMIT 1",
          q.shareholding_id,
          q.settled_at
        ),
        on: true
      )
      |> where([q, sm], sm.closing_balance == 0)
      |> group_by([q, sm], q.shareholding_id)
      |> order_by([q, sm], asc: q.shareholding_id)
      |> select([q, sm], %{shareholding_id: q.shareholding_id, settled_at: max(q.settled_at)})
      |> subquery()
      |> join(:inner, [q], sm in ShareMovement,
        on:
          sm.shareholding_id == q.shareholding_id and sm.settled_at > q.settled_at and
            sm.settled_at <= ^date
      )
      |> where([q, sm], sm.closing_balance > 0)
      |> group_by([q, sm], q.shareholding_id)
      |> order_by([q, sm], asc: q.shareholding_id)
      |> select([q, sm], %{shareholding_id: q.shareholding_id, date: min(sm.settled_at)})

    update_has_sold_out_query =
      DailyHolding
      |> join(:inner, [dh], sm in subquery(has_sold_out_query), on: sm.shareholding_id == dh.shareholding_id)
      |> where([dh], dh.date == ^date)
      |> update([dh, sm], set: [current_holding_start_date: sm.date])

    # For shareholders who have never sold out, get their earliest buy in date
    # We know they have never sold out if their current_holding_start_date is still NULL
    # Should only consider all movements less than equal to provided date
    never_sold_out_query =
      DailyHolding
      |> join(:inner, [dh], sh in subquery(shareholdings_query), on: sh.id == dh.shareholding_id)
      |> join(:inner, [dh, sh], sm in ShareMovement, on: sm.shareholding_id == sh.id)
      |> where([dh], is_nil(dh.current_holding_start_date))
      |> where([dh, sh], dh.date == sh.date)
      |> where([dh, sh, sm], sh.date >= sm.settled_at)
      # |> where([dh, sh, sm], sm.movement > 0) # We can't guarantee we will have the all time share movements due to csv upload
      |> group_by([dh, sh], sh.id)
      |> select([dh, sh, sm], %{shareholding_id: sh.id, date: min(sm.settled_at)})

    update_never_sold_out_query =
      DailyHolding
      |> join(:inner, [dh], sm in subquery(never_sold_out_query), on: sm.shareholding_id == dh.shareholding_id)
      |> where([dh], dh.date == ^date)
      |> update([dh, sm], set: [current_holding_start_date: sm.date])

    # When we have incomplete share movements due to csv upload
    # Some daily holdings might still not have current_holding_start_date
    # Set to their initial purchase date
    update_to_initial_purchase_date_query =
      DailyHolding
      |> join(:inner, [dh], sh in subquery(shareholdings_query), on: sh.id == dh.shareholding_id)
      |> where([dh], is_nil(dh.current_holding_start_date))
      |> where([dh, sh], dh.date == sh.date)
      |> update([dh, sh], set: [current_holding_start_date: sh.initial_purchase_date])

    Ecto.Multi.new()
    |> Ecto.Multi.update_all(:reset_counter_cache, reset_counter_cache_query, [])
    |> Ecto.Multi.update_all(:update_has_sold_out, update_has_sold_out_query, [])
    |> Ecto.Multi.update_all(:update_never_sold_out, update_never_sold_out_query, [])
    |> Ecto.Multi.update_all(
      :update_to_initial_purchase_date,
      update_to_initial_purchase_date_query,
      []
    )
    |> Repo.transaction(timeout: 120_000)
  end

  def get_latest_daily_holding_date_by_company_profile(%Profile{id: company_profile_id}) do
    get_latest_daily_holding_date_by_company_profile(company_profile_id)
  end

  def get_latest_daily_holding_date_by_company_profile(company_profile_id) do
    DailyHolding
    |> where(company_profile_id: ^company_profile_id)
    |> select([q], max(q.date))
    |> Repo.one()
  end

  def get_latest_daily_holding_date_by_company_profile_and_date(company_profile_id, %Date{} = date) do
    DailyHolding
    |> where(company_profile_id: ^company_profile_id)
    |> where([q], q.date <= ^date)
    |> select([q], max(q.date))
    |> Repo.one()
  end

  def get_earliest_daily_holding_date_by_company_profile(%Profile{id: company_profile_id}) do
    DailyHolding
    |> where(company_profile_id: ^company_profile_id)
    |> select([q], min(q.date))
    |> Repo.one()
  end

  def get_min_share_movement_date_for_company(company_profile_id) do
    ShareMovement
    |> where(company_profile_id: ^company_profile_id)
    |> select([sm], min(sm.settled_at))
    |> Repo.one()
  end

  def get_max_share_movement_date_for_company(company_profile_id) do
    ShareMovement
    |> where(company_profile_id: ^company_profile_id)
    |> select([sm], max(sm.settled_at))
    |> Repo.one()
  end

  @doc """
  Construct the daily holdings for a company

  Retrieve the (previous) daily holdings from the given date
  Get the share movements of the given date
  Construct the daily holdings for the given date using the above information
  """
  def construct_daily_holdings(company_profile_id, %Date{} = date) do
    Ecto.Multi.new()
    |> Ecto.Multi.delete_all(
      :delete_daily_holdings,
      DailyHolding
      |> join(:inner, [dh], sh in assoc(dh, :shareholding))
      |> where([dh, sh], sh.company_profile_id == ^company_profile_id)
      |> where([dh, sh], dh.date == ^date)
    )
    |> Ecto.Multi.run(:insert_daily_holdings, fn _, _ ->
      utc_now = NaiveDateTime.utc_now(:second)

      ShareMovement
      |> where([sm], sm.company_profile_id == ^company_profile_id)
      |> where([sm], sm.settled_at <= ^date)
      |> group_by([sm], sm.shareholding_id)
      |> having([sm], type(sum(sm.movement), :integer) > 0)
      |> select([sm], %{
        balance: type(sum(sm.movement), :integer),
        company_profile_id: type(^company_profile_id, :integer),
        date: type(^date, :date),
        shareholding_id: sm.shareholding_id,
        inserted_at: type(^utc_now, :naive_datetime),
        updated_at: type(^utc_now, :naive_datetime)
      })
      |> Repo.all()
      |> insert_all_daily_holdings()
    end)
    |> Ecto.Multi.run(:update_daily_holdings_counter_cache, fn _, _ ->
      update_daily_holdings_counter_cache(company_profile_id, date)
    end)
    |> Repo.transaction(timeout: 180_000)
  end

  @doc """
  Construct daily holdings for a specific shareholding
  """
  def construct_daily_holdings_by_shareholding(%Shareholding{id: shareholding_id, company_profile_id: company_profile_id}) do
    Ecto.Multi.new()
    |> Ecto.Multi.delete_all(
      :delete_daily_holdings,
      where(DailyHolding, shareholding_id: ^shareholding_id)
    )
    |> Ecto.Multi.run(:insert_daily_holdings, fn _, _ ->
      insert_or_skip_daily_holdings(shareholding_id, company_profile_id)
    end)
    |> Repo.transaction(timeout: 120_000)
  end

  def insert_or_skip_daily_holdings(shareholding_id, company_profile_id) do
    case get_share_movements_for_daily_holdings(shareholding_id) do
      [_ | _] = movements ->
        start_movement = find_start_movement(movements)

        if start_movement,
          do:
            create_daily_holdings_for_shareholding(
              start_movement,
              movements,
              company_profile_id,
              shareholding_id
            ),
          else: {:ok, "no daily holdings to create"}

      _ ->
        {:ok, "no share movements found"}
    end
  end

  @doc """
  Filters shareholdings, if the shareholder is already in the database we filter them out from the list
  """
  def filter_shareholdings_by_company_id(shareholdings, company_profile_id) do
    register_holder_ids = Enum.map(shareholdings, & &1.registry_holder_id)

    shareholding_lum =
      Shareholding
      |> where(company_profile_id: ^company_profile_id)
      |> where([sh], sh.registry_holder_id in ^register_holder_ids)
      |> select([sh], {sh.registry_holder_id, sh.id})
      |> Repo.all()
      |> Map.new()

    Enum.filter(shareholdings, &(not Map.has_key?(shareholding_lum, &1.registry_holder_id)))
  end

  defp get_share_movements_for_daily_holdings(shareholding_id) do
    ShareMovement
    |> where(shareholding_id: ^shareholding_id)
    |> order_by(asc: :settled_at, asc: :id)
    |> Repo.all()
  end

  defp create_daily_holdings_for_shareholding(start_movement, movements, company_profile_id, shareholding_id) do
    utc_now = NaiveDateTime.utc_now(:second)

    start_date = start_movement.settled_at
    end_date = Helper.ExDay.now_date()

    first_daily_holding = [
      %{
        balance: start_movement.closing_balance,
        company_profile_id: company_profile_id,
        current_holding_start_date: start_date,
        date: start_date,
        shareholding_id: shareholding_id,
        inserted_at: utc_now,
        updated_at: utc_now
      }
    ]

    all_daily_holdings =
      [from: start_date, until: end_date, left_open: true, right_open: false]
      |> Timex.Interval.new()
      |> Enum.map(&Timex.to_date(&1))
      |> Enum.reduce(first_daily_holding, fn date, [%{date: prev_date} = prev | _] = acc ->
        # Handle multiple cases
        # 1. holding
        # 2. churned
        # 3. returning
        # 4. upgrader or downgrader

        net_movements =
          movements
          |> Enum.filter(&(&1.settled_at == date))
          |> Enum.reduce(0, &(&2 + &1.movement))

        prev_holding =
          date
          |> Timex.shift(days: -1)
          |> Timex.compare(prev_date, :days)
          |> case do
            0 -> prev
            _ -> nil
          end

        case {net_movements, prev_holding} do
          {0, nil} ->
            acc

          {0, _} ->
            [Map.put(prev_holding, :date, date) | acc]

          {_, nil} ->
            [
              %{
                balance: net_movements,
                company_profile_id: company_profile_id,
                current_holding_start_date: date,
                date: date,
                shareholding_id: shareholding_id,
                inserted_at: utc_now,
                updated_at: utc_now
              }
              | acc
            ]

          {_, _} when net_movements + prev_holding.balance == 0 ->
            acc

          {_, _} ->
            [
              prev_holding
              |> Map.put(:date, date)
              |> Map.put(:balance, prev_holding.balance + net_movements)
              | acc
            ]
        end
      end)
      |> Enum.reverse()

    all_daily_holdings
    |> Enum.chunk_every(5_000)
    |> Enum.each(&Repo.insert_all(DailyHolding, &1))

    {:ok, Enum.count(all_daily_holdings)}
  end

  defp find_start_movement(movements) do
    # Gets the first movement where closing_balance of the day is > 0
    [%ShareMovement{settled_at: start_date} | rest] = movements

    # Get the highest id movement for that date, this is the closing balance of the day
    start_movement =
      movements |> Enum.filter(&(&1.settled_at == start_date)) |> Enum.max_by(& &1.id)

    case {start_movement.closing_balance, rest} do
      {0, [_ | _]} ->
        # Find one with positive balance
        find_start_movement(rest)

      {0, []} ->
        # Can't find any, no daily holdings to create
        nil

      {_, _} ->
        start_movement
    end
  end

  @doc """
  Returns the list of share_movements.

  ## Examples

      iex> list_share_movements()
      [%ShareMovement{}, ...]

  """
  def list_share_movements do
    Repo.all(ShareMovement)
  end

  @doc """
  Gets a single share_movement.

  Raises `Ecto.NoResultsError` if the ShareMovement does not exist.

  ## Examples

      iex> get_share_movement!(123)
      %ShareMovement{}

      iex> get_share_movement!(456)
      ** (Ecto.NoResultsError)

  """
  def get_share_movement!(id), do: Repo.get!(ShareMovement, id)

  @doc """
  Gets all share_movements for a shareholding within the given date range
  """
  def get_share_movements_by_shareholding_id(id, company_profile_id, %Date{} = start_date, %Date{} = end_date, opts \\ []) do
    default = [movement_sort_order: :asc]
    options = Keyword.merge(default, opts)
    sort_order = Keyword.get(options, :movement_sort_order)

    ShareMovement
    |> join(:inner, [sm], sh in Shareholding, on: sh.id == sm.shareholding_id and sh.id == ^id)
    |> where([sm, sh], sh.company_profile_id == ^company_profile_id)
    |> where([sm], sm.settled_at >= ^start_date)
    |> where([sm], sm.settled_at <= ^end_date)
    |> order_by([sm], [{^sort_order, sm.settled_at}, asc: sm.id])
    |> Repo.all()
  end

  @doc """
  Creates a share_movement.

  ## Examples

      iex> create_share_movement(%{field: value})
      {:ok, %ShareMovement{}}

      iex> create_share_movement(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_share_movement(attrs \\ %{}) do
    %ShareMovement{}
    |> ShareMovement.changeset(attrs)
    |> Repo.insert()
  end

  def insert_all_share_movements(share_movements) when is_list(share_movements) do
    # Need to use chunk_every because insert_all has a limit of 65535 parameters

    share_movements
    |> Enum.chunk_every(3_000)
    |> Enum.reduce(Ecto.Multi.new(), fn share_movements_chunk, multi_acc ->
      share_movement = List.first(share_movements_chunk)

      settled_at = Timex.format!(share_movement.settled_at, "{YYYY}_{0M}_{0D}")

      atom_id =
        "#{share_movement.shareholding_id}_#{settled_at}_"
        |> Kernel.<>("#{abs(share_movement.movement)}_")
        |> Kernel.<>("#{share_movement.closing_balance}")
        |> String.to_atom()

      Ecto.Multi.insert_all(
        multi_acc,
        atom_id,
        ShareMovement,
        share_movements_chunk
      )
    end)
    |> Repo.transaction(timeout: 120_000)
  end

  @doc """
  Calculate and update the counter caches on share movements table
  """
  def update_share_movements_counter_cache(company_profile_id) do
    # Estimate transaction date and price from api data
    estimate_from_api_query =
      ShareMovement
      |> join(:inner, [sm], sh in Shareholding, on: sh.id == sm.shareholding_id)
      |> where([sm, sh], sh.company_profile_id == ^company_profile_id)
      |> where([sm], not is_nil(sm.transaction_price))
      |> update([sm],
        set: [
          estimated_transaction_date: fragment("DATE(?) - interval '2 days'", sm.settled_at),
          estimated_price: sm.transaction_price
        ]
      )

    # Estimate the movement happened t minus 2 of settlement date
    # Based on current logic, the t minus 2 of Monday is Friday
    t_minus_2_query =
      ShareMovement
      |> join(:inner, [sm], sh in Shareholding, on: sm.shareholding_id == sh.id)
      |> join(
        :inner_lateral,
        [sm, sh],
        nats in fragment(
          "SELECT nats.close, nats.currency, nats.date FROM markets_timeseries_non_adjusted nats WHERE nats.company_profile_id = ? AND nats.date <= (DATE(?) - interval '2 days') ORDER BY nats.date DESC LIMIT 1",
          sh.company_profile_id,
          sm.settled_at
        ),
        on: true
      )
      |> where([sm], is_nil(sm.estimated_transaction_date) and is_nil(sm.estimated_price))
      |> where([sm, sh], sh.company_profile_id == ^company_profile_id)
      |> select([sm, sh, nats], %{
        id: sm.id,
        currency: nats.currency,
        estimated_price: nats.close,
        estimated_transaction_date: nats.date
      })

    estimate_with_t_minus_2_query =
      ShareMovement
      |> join(:inner, [sm], nats in subquery(t_minus_2_query), on: nats.id == sm.id)
      |> update([sm, nats],
        set: [
          currency: nats.currency,
          estimated_price: nats.estimated_price,
          estimated_transaction_date: nats.estimated_transaction_date
        ]
      )

    # We use the earliest timeseries for all the movements that we couldn't get their t minus 2
    estimate_with_earliest_ts_query =
      ShareMovement
      |> join(:inner, [sm], sh in Shareholding, on: sm.shareholding_id == sh.id)
      |> join(
        :inner,
        [sm, sh],
        nats in subquery(
          TimeseriesNonAdjusted
          |> distinct(asc: :company_profile_id)
          |> order_by(asc: :date)
        ),
        on: nats.company_profile_id == sh.company_profile_id
      )
      |> where([sm, sh], sh.company_profile_id == ^company_profile_id)
      |> where([sm], is_nil(sm.estimated_transaction_date) and is_nil(sm.estimated_price))
      |> update([sm, sh, nats],
        set: [
          currency: nats.currency,
          estimated_price: nats.close,
          estimated_transaction_date: nats.date
        ]
      )

    Ecto.Multi.new()
    |> Ecto.Multi.update_all(:estimate_from_metadata, estimate_from_api_query, [])
    |> Ecto.Multi.update_all(:estimate_with_t_minus_2, estimate_with_t_minus_2_query, [])
    |> Ecto.Multi.update_all(:estimate_with_earliest_ts, estimate_with_earliest_ts_query, [])
    |> Repo.transaction(timeout: 180_000)
  end

  @doc """
  Updates a share_movement.

  ## Examples

      iex> update_share_movement(share_movement, %{field: new_value})
      {:ok, %ShareMovement{}}

      iex> update_share_movement(share_movement, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_share_movement(%ShareMovement{} = share_movement, attrs) do
    share_movement
    |> ShareMovement.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a share_movement.

  ## Examples

      iex> delete_share_movement(share_movement)
      {:ok, %ShareMovement{}}

      iex> delete_share_movement(share_movement)
      {:error, %Ecto.Changeset{}}

  """
  def delete_share_movement(%ShareMovement{} = share_movement) do
    Repo.delete(share_movement)
  end

  def delete_share_movements_within_date_range(company_profile_id, %Date{} = start_date, %Date{} = end_date) do
    shareholdings_query =
      Shareholding
      |> where([sh], sh.company_profile_id == ^company_profile_id)
      |> select([sh], sh.id)

    ShareMovement
    |> where([sm], sm.shareholding_id in subquery(shareholdings_query))
    |> where([sm], sm.settled_at >= ^start_date)
    |> where([sm], sm.settled_at <= ^end_date)
    |> Repo.delete_all()
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking share_movement changes.

  ## Examples

      iex> change_share_movement(share_movement)
      %Ecto.Changeset{data: %ShareMovement{}}

  """
  def change_share_movement(%ShareMovement{} = share_movement, attrs \\ %{}) do
    ShareMovement.changeset(share_movement, attrs)
  end

  @doc """
  Returns movements of top 50 shareholders on a given date

  The top 50 includes both holders that were top 50 at the start or end of day
  """
  def get_top_50_shareholders_movements(company_profile_id, %Date{} = date) do
    # The top 50 holders at the start of day is equivalent to top 50 holders on the previous end of day
    top_50_at_start_query =
      company_profile_id
      |> top_50_query(Timex.shift(date, days: -1))
      |> select(%{})
      |> select_merge([_, sh], %{shareholding_id: sh.id})
      |> select_merge([_, sh], %{account_name: sh.account_name})

    top_50_at_end_query =
      company_profile_id
      |> top_50_query(date)
      |> select(%{})
      |> select_merge([_, sh], %{shareholding_id: sh.id})
      |> select_merge([_, sh], %{account_name: sh.account_name})

    top_50_at_start_query
    |> subquery()
    |> union(^top_50_at_end_query)
    |> subquery()
    |> join(:inner, [q], sm in ShareMovement, on: sm.shareholding_id == q.shareholding_id)
    |> where([_, sm], sm.settled_at == ^date)
    |> where([_, sm], sm.movement_type not in @movement_type_consolidation)
    |> group_by([q, sm], [q.shareholding_id, q.account_name, sm.currency])
    |> select([q], q)
    |> select_merge([_, sm], %{
      currency: fragment("CASE WHEN ? = 'GBX' THEN 'GBP' ELSE ? END", sm.currency, sm.currency),
      movement: type(sum(sm.movement), :integer),
      movement_value:
        fragment(
          "ABS(SUM(? * ? * (CASE WHEN ? = 'GBX' THEN 0.01 ELSE 1 END)))",
          sm.movement,
          sm.estimated_price,
          sm.currency
        )
    })
    |> subquery()
    |> order_by([q], asc: q.movement, asc: q.account_name, asc: q.shareholding_id)
    |> Repo.all(timeout: :infinity)
  end

  def top_50_query(company_profile_id, %Date{} = date) do
    DailyHolding
    |> join(:inner, [dh], sh in assoc(dh, :shareholding))
    |> where([dh], dh.date == ^date)
    |> where([_, sh], sh.company_profile_id == ^company_profile_id)
    |> order_by([dh], desc: dh.balance, desc: dh.shareholding_id)
    |> limit(50)
  end

  def init_nominee_contacts_from_top_50_shareholders do
    Enum.map(Gaia.Companies.list_all_clients(), fn %{is_demo: false, is_trial: false, id: company_profile_id} ->
      date = get_latest_daily_holding_date_by_company_profile(company_profile_id)

      if date do
        company_profile_id
        |> top_50_query(date)
        |> join(:inner, [q], sh in assoc(q, :shareholding))
        |> select([q, sh], %{account_name: sh.account_name})
        |> Repo.all()
        |> add_account_to_nominees_contacts()
      end
    end)
  end

  defp add_account_to_nominees_contacts([%{account_name: account_name} | accounts]) do
    nominee_terms = [
      "noms",
      "nominee",
      "nominees",
      "netwealth",
      "finclear",
      "custodians",
      "custody",
      "comsec",
      "morgan stanley"
    ]

    nominee_terms
    |> Enum.any?(fn term -> String.contains?(String.downcase(account_name), term) end)
    |> case do
      true ->
        Logger.info("Create nominee account for #{account_name}")

        Gaia.BeneficialOwners.create_nominee_contact(%{
          account_name: account_name
        })

      _ ->
        :ok
    end

    add_account_to_nominees_contacts(accounts)
  end

  defp add_account_to_nominees_contacts([]) do
    :ok
  end

  def get_latest_share_movement(shareholding_id) do
    query =
      from(sm in ShareMovement,
        where:
          sm.shareholding_id == ^shareholding_id and
            sm.movement_type not in @movement_type_consolidation,
        order_by: [desc: sm.settled_at, desc: sm.inserted_at],
        limit: 1
      )

    Repo.one(query)
  end

  def get_shareholding_share_count_rank(shareholding_id, company_profile_id) do
    rank_query =
      from(sh in Shareholding,
        where: sh.company_profile_id == ^company_profile_id,
        select: %{
          id: sh.id,
          rank: fragment("RANK() OVER (ORDER BY ? DESC NULLS LAST, ? DESC)", sh.share_count, sh.id)
        }
      )

    query =
      from(sh in subquery(rank_query),
        where: sh.id == ^shareholding_id,
        select: sh.rank
      )

    Repo.one(query)
  end

  def has_shareholding_participated_in_spp(shareholding_id, company_profile_id) do
    query =
      from(sm in ShareMovement,
        where:
          sm.shareholding_id == ^shareholding_id and
            sm.company_profile_id == ^company_profile_id and
            fragment(
              "LOWER(REPLACE(?, ' ', '')) IN (?, ?)",
              sm.movement_type,
              "sharepurchaseplan",
              "spp"
            )
      )

    Repo.aggregate(query, :count) > 0
  end

  def company_profile_has_had_spp(company_profile_id) do
    query =
      from(sm in ShareMovement,
        where:
          sm.company_profile_id == ^company_profile_id and
            fragment(
              "LOWER(REPLACE(?, ' ', '')) IN (?, ?)",
              sm.movement_type,
              "sharepurchaseplan",
              "spp"
            )
      )

    Repo.aggregate(query, :count) > 0
  end

  def company_profile_has_had_placement(company_profile_id) do
    query =
      from(p in Participant,
        where: p.company_profile_id == ^company_profile_id
      )

    Repo.aggregate(query, :count) > 0
  end

  @doc """
  Prioritises using cached token if not expired
  https://developer.automic.com.au/#_client_credentials
  Token expires after 30 minutes.

  Token applies for single API key, but we are using the same API key for all clients now,
  so only need to store one token
  """
  def automic_get_access_token do
    with %{expires_at: expires_at, token: access_token} <- get_token_from_repo(),
         :gt <- NaiveDateTime.compare(expires_at, NaiveDateTime.utc_now(:second)),
         {:check_token, {:ok, _response}} <- {:check_token, Automic.get_issuer(access_token)} do
      {:ok, %{"access_token" => access_token}}
    else
      _ ->
        grant_and_store_access_token()
    end
  end

  defp get_token_from_repo do
    Token
    |> order_by([t], desc: t.id)
    |> where(service: "automic")
    |> Repo.one()
  end

  def set_token_in_repo(%{token: token, expires_at: expires_at}) do
    params = %{service: "automic", token: token, expires_at: expires_at}

    Multi.new()
    |> Multi.delete_all(
      :delete_existing_tokens,
      where(Token, [t], t.service == "automic")
    )
    |> Multi.insert(:create_new_token, Token.changeset(%Token{}, params))
    |> Repo.transaction()
  end

  defp grant_and_store_access_token do
    # tokens expires in 30 mins, but reducing to 29 mins
    # as it keeps failing with `invalid.token` error
    case Automic.grant_access_token() do
      {:ok, %{"access_token" => access_token}} ->
        set_token_in_repo(%{
          expires_at: NaiveDateTime.add(NaiveDateTime.utc_now(:second), 29, :minute),
          token: access_token
        })

        {:ok, %{"access_token" => access_token}}

      {:error, error} ->
        {:error, error}
    end
  end

  @doc """
  Get total number of shareholders from shareholdings table
  """
  def count_total_shareholders_for_company_profile(company_profile_id) do
    company_profile_id
    |> get_shareholdings_for_company_profile()
    |> where([sh], sh.share_count > 0)
    |> Repo.aggregate(:count)
  end

  @doc """
  Average holding size (number of shares) from shareholdings_table
  """
  def average_holding_size_for_company_profile(company_profile_id) do
    company_profile_id
    |> get_shareholdings_for_company_profile()
    |> select([sh], avg(sh.share_count))
    |> Repo.one()
    |> case do
      nil -> 0.0
      val -> Decimal.to_float(val)
    end
  end

  @doc """
  Average holding length (in days) from shareholdings_table
  """
  def average_holding_length_for_company_profile(company_profile_id) do
    company_profile_id
    |> get_shareholdings_for_company_profile()
    |> select(
      [sh],
      avg(fragment("DATE_PART('day', CURRENT_TIMESTAMP - ?)", sh.current_holding_start_date))
    )
    |> Repo.one()
    |> case do
      nil -> 0.0
      val -> val
    end
  end

  @doc """
  Get number of HNW investors
  """
  def count_hnw_shareholders_for_company_profile(company_profile_id) do
    company_profile_id
    |> get_shareholdings_for_company_profile()
    |> where([sh], not is_nil(sh.hnw_behaviour))
    |> Repo.aggregate(:count)
  end

  @doc """
  Get number of active (one trade within last 3 months) traders
  """
  def count_active_shareholders_for_company_profile(company_profile_id) do
    company_profile_id
    |> get_joined_shareholding_and_share_movements_for_company_profile()
    |> where([sm], fragment("DATE_PART('day', NOW() - ?) < 90", sm.settled_at))
    |> distinct([sh], sh.shareholding_id)
    |> Repo.aggregate(:count)
  end

  @doc """
  Get number shareholders with a current holding profit
  """
  def count_profit_shareholders_for_company_profile(company_profile_id) do
    company_profile_id
    |> get_shareholdings_for_company_profile()
    |> where([sh], sh.estimated_profit_loss > 0.0)
    |> Repo.aggregate(:count)
  end

  @doc """
  Get number shareholders with a phone number
  """
  def count_shareholders_with_phone_number_for_company_profile(company_profile_id) do
    company_profile_id
    |> get_shareholdings_for_company_profile()
    |> where([sh], not is_nil(sh.phone_number))
    |> Repo.aggregate(:count)
  end

  @doc """
  Get number shareholders with an email
  """
  def count_shareholders_with_email_for_company_profile(company_profile_id) do
    company_profile_id
    |> get_shareholdings_for_company_profile()
    |> where([sh], not is_nil(sh.email))
    |> Repo.aggregate(:count)
  end

  # Private function to query shareholdings table
  defp get_shareholdings_for_company_profile(company_profile_id) do
    from(sh in Shareholding,
      where: sh.company_profile_id == ^company_profile_id and sh.share_count > 0
    )
  end

  # Private function to query share movements table
  # defp get_share_movements_for_company_profile(company_profile_id) do
  #   from(sm in ShareMovement,
  #     where: sm.company_profile_id == ^company_profile_id and sh.share_count > 0
  #   )
  # end

  # Private function to query joined shareholdings and share_movement table
  defp get_joined_shareholding_and_share_movements_for_company_profile(company_profile_id) do
    from(sm in ShareMovement,
      join: sh in Shareholding,
      on: sm.shareholding_id == sh.id,
      where: sm.company_profile_id == ^company_profile_id and sh.share_count > 0
    )
  end

  def count_qualified_investors_for_company_profile(company_profile_id) do
    Contact
    |> where(
      [c],
      (c.is_nominated_shareholder or c.hnw_status == ^:nominated_cert_verified) and
        c.company_profile_id == ^company_profile_id
    )
    |> Repo.aggregate(:count)
  end

  def get_raise_spp_shareholder_stats(company_profile_id) do
    %{
      total_shareholders: count_total_shareholders_for_company_profile(company_profile_id),
      average_hold_size: average_holding_size_for_company_profile(company_profile_id),
      average_hold_length: average_holding_length_for_company_profile(company_profile_id),
      total_qual: count_qualified_investors_for_company_profile(company_profile_id),
      total_hnws: count_hnw_shareholders_for_company_profile(company_profile_id),
      total_active: count_active_shareholders_for_company_profile(company_profile_id),
      total_profit: count_profit_shareholders_for_company_profile(company_profile_id)
    }
  end

  def test_welcome_new_shareholder(attrs, shareholder_type, shareholder_welcome_enabled) do
    shareholder_type
    |> Gaia.Factory.Registers.shareholding(attrs)
    |> Repo.preload(:contact)
    |> unsubscribe_new_shareholder_email_subscriptions()
    |> maybe_enqueue_test_welcome_shareholder_email(shareholder_welcome_enabled)
  end

  def unsubscribe_new_shareholder_email_subscriptions(
        %Shareholding{company_profile_id: company_profile_id, contact: %Contact{} = contact} = shareholding
      ) do
    bulk_unsubscribe_new_shareholding_contacts([contact], company_profile_id)

    shareholding
  end

  def unsubscribe_new_shareholder_email_subscriptions(shareholding), do: shareholding

  def maybe_enqueue_test_welcome_shareholder_email(
        %Shareholding{contact: %Contact{} = contact, company_profile_id: company_profile_id},
        true
      ) do
    Gaia.Flows.NewShareholderWelcome.enqueue_welcome_emails([contact], company_profile_id)
  end

  def maybe_enqueue_test_welcome_shareholder_email(_shareholding, false) do
    :skip
  end

  def maybe_enqueue_test_welcome_shareholder_email(_shareholding, _shareholder_welcome_enabled) do
    {:error, "No test shareholder created"}
  end

  def bulk_unsubscribe_new_shareholding_contacts(contacts, company_profile_id) do
    # Make sure unsubscribed new shareholding contacts belong to the correct company profile
    contacts
    |> Repo.preload([:company_profile])
    |> Enum.filter(&(&1.company_profile_id == company_profile_id))
    |> case do
      [] ->
        {:ok, "No contact unsubscriptions added"}

      [
        %Contact{
          company_profile: %Profile{
            global_unsubscribe_on_registry_import: global_unsubscribe_on_registry_import,
            unsubscribe_scopes_on_registry_import: unsubscribe_scopes_on_registry_import
          }
        }
        | _
      ] = new_contacts ->
        if global_unsubscribe_on_registry_import do
          bulk_global_unsubscribe_contacts(new_contacts, company_profile_id)
        else
          bulk_unsubscribe_contacts_email_subscriptions(
            new_contacts,
            company_profile_id,
            unsubscribe_scopes_on_registry_import
          )
        end
    end
  end

  # Bulk global unsubscribe new shareholding contacts
  def bulk_global_unsubscribe_contacts(contacts, company_profile_id) do
    utc_now = NaiveDateTime.utc_now(:second)

    contact_ids =
      contacts
      |> Enum.filter(&(&1.company_profile_id == company_profile_id))
      |> Enum.map(& &1.id)

    Ecto.Multi.new()
    |> Ecto.Multi.run(:bulk_global_unsubscribe, fn repo, _ ->
      contact_ids
      |> Enum.chunk_every(1000)
      |> Enum.each(fn chunk ->
        repo.insert_all(
          ContactGlobalUnsubscribe,
          Enum.map(
            chunk,
            &%{
              company_profile_id: company_profile_id,
              contact_id: &1,
              inserted_at: utc_now,
              updated_at: utc_now
            }
          ),
          on_conflict: :nothing
        )
      end)

      {:ok, :done}
    end)
    |> Repo.transaction()
  end

  # Bulk unsubscribe new contacts from specific scopes
  def bulk_unsubscribe_contacts_email_subscriptions(contacts, company_profile_id, unsubscribe_scopes) do
    utc_now = NaiveDateTime.utc_now(:second)

    contact_ids =
      contacts
      |> Enum.filter(&(&1.company_profile_id == company_profile_id))
      |> Enum.map(& &1.id)

    unsubscribe_scopes =
      Enum.map(unsubscribe_scopes, &(&1 |> String.downcase() |> String.to_atom()))

    Ecto.Multi.new()
    |> Ecto.Multi.run(:bulk_upsert_comms_contact_unsubscribes, fn repo, _changes ->
      comms_unsubscribes =
        Enum.flat_map(contact_ids, fn contact_id ->
          Enum.map(
            unsubscribe_scopes,
            &%{
              company_profile_id: company_profile_id,
              contact_id: contact_id,
              scope: &1,
              inserted_at: utc_now,
              updated_at: utc_now
            }
          )
        end)

      comms_unsubscribes
      |> Enum.chunk_every(1000)
      |> Enum.each(fn chunk ->
        repo.insert_all(ContactUnsubscribe, chunk, on_conflict: :nothing)
      end)

      {:ok, :done}
    end)
    |> Repo.transaction()
  end

  def calculate_investor_shareholding_at_date(nil, _date), do: {:ok, %{share_count: nil, date: nil}}

  def calculate_investor_shareholding_at_date(investor_user_id, date) do
    total_shares =
      Repo.one(
        from(dh in DailyHolding,
          join: s in assoc(dh, :shareholding),
          join: i in Gaia.Investors.User,
          on: s.contact_id == i.contact_id,
          where:
            i.id == ^investor_user_id and
              s.contact_id == i.contact_id and
              dh.date == ^date,
          # Cast the sum to integer
          select: type(sum(dh.balance), :integer)
        )
      )

    {:ok,
     %{
       share_count: total_shares,
       date: date
     }}
  end

  def calculate_investor_shareholding_by_date(nil, _date, _start_date), do: {:ok, %{share_count: nil, date: nil}}

  def calculate_investor_shareholding_by_date(investor_user_id, date, start_date) do
    case get_closing_balance_for_investor_shareholding(investor_user_id, date) do
      0 ->
        {:ok, %{share_count: 0, date: date}}

      _ ->
        latest_date = get_the_latest_date_for_investor_shareholding(investor_user_id, start_date, date)

        case latest_date do
          nil ->
            {:ok, %{share_count: nil, date: nil}}

          _ ->
            total_shares = get_total_shares(investor_user_id, latest_date)
            {:ok, %{share_count: total_shares, date: latest_date}}
        end
    end
  end

  defp get_closing_balance_for_investor_shareholding(investor_user_id, date) do
    contact_shareholdings_query =
      from(i in Gaia.Investors.User,
        join: s in Shareholding,
        on: s.contact_id == i.contact_id,
        where: i.id == ^investor_user_id,
        select: s.id
      )

    latest_movement_query =
      from(sm in ShareMovement,
        where: sm.shareholding_id in subquery(contact_shareholdings_query),
        where: sm.settled_at <= ^date,
        group_by: sm.shareholding_id,
        select: %{
          shareholding_id: sm.shareholding_id,
          latest_settled_at: max(sm.settled_at)
        }
      )

    closing_balance =
      Repo.one(
        from(sm in ShareMovement,
          join: lm in subquery(latest_movement_query),
          on: sm.shareholding_id == lm.shareholding_id and sm.settled_at == lm.latest_settled_at,
          select: type(sum(sm.closing_balance), :integer)
        )
      )

    closing_balance
  end

  defp get_the_latest_date_for_investor_shareholding(investor_user_id, start_date, end_date) do
    Repo.one(
      from(dh in DailyHolding,
        join: s in assoc(dh, :shareholding),
        join: i in Gaia.Investors.User,
        on: s.contact_id == i.contact_id,
        where: i.id == ^investor_user_id and dh.date >= ^start_date and dh.date <= ^end_date,
        order_by: [desc: dh.date],
        limit: 1,
        select: dh.date
      )
    )
  end

  defp get_total_shares(investor_user_id, date) do
    Repo.one(
      from(dh in DailyHolding,
        join: s in assoc(dh, :shareholding),
        join: i in Gaia.Investors.User,
        on: s.contact_id == i.contact_id,
        where: i.id == ^investor_user_id and dh.date == ^date,
        select: type(sum(dh.balance), :integer)
      )
    )
  end

  def total_shares_for_company_by_date_or_most_recent(company_profile_id, date) do
    by_date = total_shares_for_company_by_date(company_profile_id, date)

    case by_date do
      nil ->
        # If no holdings for that date, get the most recent date first, then sum for that date
        most_recent_date_query =
          DailyHolding
          |> where([dh], dh.company_profile_id == ^company_profile_id)
          |> order_by([dh], desc: dh.date)
          |> limit(1)
          |> select([dh], dh.date)

        case Gaia.Repo.one(most_recent_date_query) do
          nil ->
            nil

          most_recent_date ->
            total_shares_for_company_by_date(company_profile_id, most_recent_date)
        end

      _ ->
        by_date
    end
  end

  def total_shares_for_company_by_date(_company_profile_id, nil), do: nil

  def total_shares_for_company_by_date(company_profile_id, date) do
    DailyHolding
    |> where([dh], dh.company_profile_id == ^company_profile_id and dh.date == ^date)
    |> select([dh], type(sum(dh.balance), :integer))
    |> Gaia.Repo.one()
  end

  def add_name_to_all_contacts_with_shareholdings_and_no_first_and_last_name do
    chunk_size = 5000

    Ecto.Multi.run(Ecto.Multi.new(), :contacts_with_shareholdings_but_no_first_name_and_last_name, fn _, _ ->
      contacts_with_shareholdings_but_no_first_name_and_last_name =
        from(c in Contact,
          join: sh in Shareholding,
          on: sh.contact_id == c.id,
          where: is_nil(c.first_name) and is_nil(c.last_name)
        )
        |> Repo.all()
        |> Repo.preload(:shareholdings)

      Enum.chunk_every(contacts_with_shareholdings_but_no_first_name_and_last_name, chunk_size)
    end)
  end
end
