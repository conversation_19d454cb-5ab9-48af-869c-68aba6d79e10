defmodule HadesWeb.StringHelpers do
  @moduledoc """
  Helper functions for outputting text
  """

  def truncate(string, length \\ 20) do
    case String.length(string) do
      len when len <= length ->
        string

      _ ->
        truncate_string(string, length)
    end
  end

  defp truncate_string(string, length) do
    string
    |> String.slice(0..length)
    |> String.trim_trailing()
    |> Kernel.<>("...")
  end

  def format_date(date, format \\ "%Y-%m-%d")
  def format_date(nil, _), do: ""

  def format_date(%NaiveDateTime{} = naive_datetime, format) do
    naive_datetime
    |> NaiveDateTime.to_date()
    |> format_date(format)
  end

  def format_date(%Date{} = date, format) do
    Timex.format!(date, format, :strftime)
  rescue
    _ ->
      Timex.format!(date, "%Y-%m-%d", :strftime)
  end

  def format_date(date, _), do: date
end
