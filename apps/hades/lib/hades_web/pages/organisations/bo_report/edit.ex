defmodule HadesWeb.OrganisationsLive.BoReport.Edit do
  @moduledoc """
  Edit page for BO reports
  """
  use HadesWeb, :live_view

  alias Gaia.BeneficialOwners

  on_mount(HadesWeb.AdminUserLiveAuth)

  @page_title "BO Report - Edit"

  def mount(%{"market" => market, "company_profile_id" => company_profile_id, "id" => report_id}, _session, socket) do
    menu = String.to_atom("#{market}_organisations")
    report = BeneficialOwners.get_report_by_id!(report_id)
    ticker = Gaia.Markets.get_ticker_by(%{company_profile_id: company_profile_id})

    athena_url =
      "#{Application.get_env(:helper, :athena_url)}/#{Gaia.Markets.Ticker.resolve_market_listing_key(ticker)}"

    all_report_candidates =
      BeneficialOwners.list_cascaded_beneficial_owner_report_candidates_by_report(
        report_id,
        :all
      )

    Phoenix.PubSub.subscribe(Gaia.PubSub, "bo_report_candidates_#{report_id}")

    authorisation_letters =
      BeneficialOwners.get_authorisation_letter_with_company_profile_by_company_profile_id(company_profile_id)

    company_profile = Gaia.Companies.get_profile_with_preload(company_profile_id, :ticker)

    total_shares_company =
      report.total_shares ||
        Gaia.Registers.total_shares_for_company_by_date_or_most_recent(
          company_profile_id,
          report.report_date
        )

    socket =
      socket
      |> assign(:menu, menu)
      |> assign(:company_profile_id, company_profile_id)
      |> assign(:page_title, @page_title)
      |> assign(:market, market)
      |> assign(:show_select_nominees_modal, false)
      |> assign(:show_upload_past_placement_report_modal, false)
      |> assign(:show_set_past_report_modal, false)
      |> assign(:nominees, [])
      |> assign(:report_id, report_id)
      |> assign(:all_report_candidates, all_report_candidates)
      |> assign(:company_profile, company_profile)
      |> assign(:authorisation_letters, authorisation_letters)
      |> assign(:report, report)
      |> assign(:editing_report, nil)
      |> assign(:edit_form, %{total_shares: total_shares_company, report_date: report.report_date})
      |> assign(:total_shares_company, total_shares_company)
      |> allow_upload(:asic_extract, accept: ~w(.pdf), max_entries: 1, max_file_size: 10_000_000)
      |> assign(:processing_status, :ready)
      |> assign(:processing_message, nil)
      |> assign(:total_chunks, 0)
      |> assign(:expanded_parents, %{})
      |> assign(:processed_chunks, 0)
      |> assign(:status_filter, nil)
      |> assign(:type_filter, nil)
      |> assign(:athena_url, athena_url)
      |> assign(:sent_filter, nil)
      |> assign(:past_report_date, nil)

    {:ok, socket}
  end

  def render(assigns) do
    ~H"""
    <div class="flex flex-col gap-4">
      <div class="flex items-center justify-between">
        <PC.button
          label="Back"
          link_type="live_redirect"
          to={
            Routes.organisations_info_path(
              @socket,
              :index,
              @market,
              @company_profile_id,
              "boreport"
            )
          }
          color="light"
          icon="hero-arrow-left"
        />

        <div class="flex items-center space-x-3">
          <PC.icon_button size="sm" phx-click="refresh_candidates" tooltip="Refresh candidates">
            <%= if @processing_status == :started do %>
              <PC.icon name="hero-arrow-path-rounded-square" class="h-5 w-5 animate-spin" />
            <% else %>
              <PC.icon name="hero-arrow-path-rounded-square" class="h-5 w-5" />
            <% end %>
          </PC.icon_button>
          <%= if !is_candidates_empty?(@all_report_candidates) and @expanded_parents != %{} do %>
            <PC.icon_button size="sm" phx-click="collapse_all" tooltip="Collapse all candidates">
              <PC.icon name="hero-eye-slash" class="h-5 w-5" />
            </PC.icon_button>
          <% end %>
          <%= if @processing_message do %>
            <span class="text-gray-500 dark:text-gray-400 text-sm">
              {@processing_message}
            </span>
          <% end %>
          <div x-data="{ open: false }" class="relative" phx-update="replace" id="actions-dropdown">
            <PC.button
              @click="open = !open"
              @keydown.escape.window="open = false"
              @click.away="open = false"
              title="Actions"
              icon="hero-ellipsis-horizontal"
              size="sm"
            >
              <span>Actions</span>
            </PC.button>

            <div
              x-cloak
              x-show="open"
              x-transition:enter="transition ease-out duration-100"
              x-transition:enter-start="transform opacity-0 scale-95"
              x-transition:enter-end="transform opacity-100 scale-100"
              x-transition:leave="transition ease-in duration-75"
              x-transition:leave-start="transform opacity-100 scale-100"
              x-transition:leave-end="transform opacity-0 scale-95"
              class="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50"
            >
              <div class="py-1">
                <%= if !is_candidates_empty?(@all_report_candidates) do %>
                  <!-- Data Management Section -->
                  <div class="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase">
                    Data Management
                  </div>

                  <button
                    class="flex w-full items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                    phx-click="add_top_level"
                  >
                    <PC.icon name="hero-plus" class="w-4 h-4 mr-3" />
                    <span>Add New Candidate</span>
                  </button>

                  <button
                    class="flex w-full items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                    phx-click="restart"
                    data-confirm="This will reset the report to the initial stage, deleting all current candidates. Are you sure you want to proceed?"
                  >
                    <PC.icon name="hero-receipt-refund" class="w-4 h-4 mr-3" />
                    <span>Restart</span>
                  </button>

                  <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>

                  <div class="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase">
                    Actions
                  </div>

                  <%= if @report.stage != :done do %>
                    <button
                      class="flex w-full items-center px-4 py-2 text-sm text-blue-700 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                      phx-click="go_to_next_stage"
                      data-confirm="All children's current shares will be saved as last_report_candidate_shares and reset to 0. Latest registry shares will be updated for top-level shareholders, and new shareholders will be synced in. Are you sure you want to proceed?"
                    >
                      <PC.icon name="hero-arrow-right-circle" class="w-4 h-4 mr-3" />
                      <span>
                        {if @report.stage == :processing_stage_1,
                          do: "Move to stage 2",
                          else: "Change stage to done"}
                      </span>
                    </button>
                  <% end %>

                  <%= if @report.is_past do %>
                    <button
                      class="flex w-full items-center px-4 py-2 text-sm text-violet-700 dark:text-violet-400 hover:bg-gray-100 dark:hover:bg-violet-700"
                      data-confirm="This will sync the future report with data from the current report. Are you sure you are ready?"
                      phx-click="sync_future_report"
                    >
                      <PC.icon name="hero-arrow-up" class="w-4 h-4 mr-3" />
                      <span>Sync Future Report</span>
                    </button>
                  <% end %>

                  <button
                    class="flex w-full items-center px-4 py-2 text-sm text-lime-700 dark:text-lime-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                    phx-click={if @report.is_past, do: "sync_participants", else: "sync_accounts"}
                    data-confirm={
                      if @report.is_past,
                        do:
                          "This will sync only participants for this past BO report. Are you sure you are ready?",
                        else:
                          "Only candidates with status 'done' or 'failed' will be synced. Are you sure you are ready?"
                    }
                  >
                    <PC.icon name="hero-paper-airplane" class="w-4 h-4 mr-3" />
                    <span>
                      {if @report.is_past, do: "Sync Participants Only", else: "Sync to Accounts"}
                    </span>
                  </button>

                  <a
                    href={"https://fresh.metabaseapp.com/question/5446-report?report_id=#{@report_id}"}
                    target="_blank"
                    rel="noopener noreferrer"
                    class="flex w-full items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                  >
                    <PC.icon name="hero-arrow-down-tray" class="w-4 h-4 mr-3" />
                    <span>Download Report</span>
                  </a>
                <% else %>
                  <!-- Fetch Data Section -->
                  <div class="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase">
                    Fetch Data
                  </div>
                  <button
                    class="flex w-full items-center px-4 py-2 text-sm text-indigo-700 dark:text-indigo-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                    phx-click="set_past_report"
                  >
                    <PC.icon name="hero-clock" class="w-4 h-4 mr-3" />
                    <span>Set Past Report</span>
                  </button>

                  <%= if @company_profile.ticker.market_key in [:lse, :aqse] do %>
                    <button
                      class="flex w-full items-center px-4 py-2 text-sm text-indigo-700 dark:text-indigo-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                      phx-click="fetch_nominees_init"
                    >
                      <PC.icon name="hero-building-office-2" class="w-4 h-4 mr-3" />
                      <span>Fetch Nominees</span>
                    </button>
                  <% else %>
                    <button
                      class="flex w-full items-center px-4 py-2 text-sm text-indigo-700 dark:text-indigo-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                      phx-click="fetch_top_50"
                    >
                      <PC.icon name="hero-arrow-down-tray" class="w-4 h-4 mr-3" />
                      <span>Fetch Top 50</span>
                    </button>
                    <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>

                    <button
                      class="flex w-full items-center px-4 py-2 text-sm text-indigo-700 dark:text-indigo-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                      phx-click="upload_past_placement_report"
                    >
                      <PC.icon name="hero-document-arrow-down" class="w-4 h-4 mr-3" />
                      <span>Upload Past Report</span>
                    </button>
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
          <PC.button
            color="light"
            label="View in Athena"
            class="text-gray-700 dark:text-gray-300"
            icon="hero-arrow-top-right-on-square"
            link_type="a"
            target="_blank"
            to={@athena_url <> "/investors/beneficial-owners/reports/#{@report.id}"}
            size="sm"
          />
        </div>
      </div>

      <div class="flex items-center">
        <span class="text-gray-800 dark:text-gray-100 text-sm">
          {@company_profile.name}
          <span class="text-gray-500 dark:text-gray-400">(report ID: {@report_id})</span>
        </span>
        <span class="mx-2">|</span>
        <%= if @editing_report && @editing_report == :report_date do %>
          <form phx-change="update_report" class="flex items-center" phx-submit="prevent_default">
            <label class="text-gray-500 dark:text-gray-400 text-sm mr-2">Report date:</label>
            <div class="inline-edit">
              <input
                type="date"
                name="report_date"
                value={@edit_form.report_date}
                class="bg-transparent border-0 border-b border-dashed border-gray-400 focus:border-indigo-500 focus:ring-0 w-full text-sm dark:text-gray-300 p-0 m-0"
                x-init="if (!document.activeElement || document.activeElement === document.body) { $el.focus(); $el.setSelectionRange($el.value.length, $el.value.length); }"
                phx-keydown="keydown"
              />
            </div>
          </form>
        <% else %>
          <span
            class="text-gray-800 dark:text-gray-100 text-sm flex items-center cursor-pointer"
            phx-click="edit_report_date"
          >
            Report date: {if @report.report_date do
              format_date(@report.report_date, "{0D}/{0M}/{YYYY}")
            else
              "N/A"
            end}
          </span>
        <% end %>
        <span class="mx-2">|</span>
        <%= if @editing_report && @editing_report == :total_shares do %>
          <form phx-change="update_report" class="flex items-center" phx-submit="prevent_default">
            <label class="text-gray-500 dark:text-gray-400 text-sm mr-2">Total shares:</label>
            <div class="inline-edit">
              <input
                type="number"
                name="total_shares"
                value={Map.get(@edit_form, :total_shares, @total_shares_company)}
                class="bg-transparent border-0 border-b border-dashed border-gray-400 focus:border-indigo-500 focus:ring-0 w-full text-sm dark:text-gray-300 p-0 m-0"
                x-init="if (!document.activeElement || document.activeElement === document.body) { $el.focus(); $el.setSelectionRange($el.value.length, $el.value.length); }"
                phx-keydown="keydown"
              />
            </div>
          </form>
        <% else %>
          <span
            class="text-gray-800 dark:text-gray-100 text-sm flex items-center cursor-pointer"
            phx-click="edit_shares"
          >
            Total shares:
            <%= if @total_shares_company do %>
              {format_number_with_commas(@total_shares_company)}
            <% else %>
              0
            <% end %>
          </span>
        <% end %>
        <span class="mx-2">|</span>
        <span class="text-gray-500 dark:text-gray-400 text-sm flex items-center">
          Last synced:
          <%= if @report.sync_at do %>
            {melbourne_time(@report.sync_at)}
          <% else %>
            Never
          <% end %>
        </span>
        <span class="mx-2">|</span>
        <span class="text-gray-500 dark:text-gray-400 text-sm flex items-center">
          Stage:&nbsp;{stage_label(@report.stage)}
        </span>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
        <.live_component
          module={HadesWeb.Pages.Organisations.BoReport.ReportCandidate.Table}
          id="bo-report-candidates-table"
          all_report_candidates={@all_report_candidates}
          authorisation_letters={@authorisation_letters}
          company_profile={@company_profile}
          status_filter={@status_filter}
          type_filter={@type_filter}
          sent_filter={@sent_filter}
        />

        <PC.modal
          :if={@show_upload_past_placement_report_modal == true}
          id="upload_past_placement_report-modal"
          show
          on_cancel={JS.push("upload_past_placement_report_cancel")}
          title="Upload Past Placement Report"
        >
          <.live_component
            module={HadesWeb.Pages.Organisations.BoReport.PastReportForm}
            id="past_report_form"
            report_id={@report_id}
            company_profile_id={@company_profile_id}
          />
        </PC.modal>

        <PC.modal
          :if={@show_set_past_report_modal == true}
          id="set_past_report-modal"
          show
          on_cancel={JS.push("set_past_report_cancel")}
          title="Set Past Report"
        >
          <form phx-change="update_past_report_date" phx-submit="confirm_set_past_report">
            <div class="flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-700 pb-4">
              <input
                type="date"
                name="past_report_date"
                id="past_report_date"
                value={@past_report_date}
              />
              <PC.button class="mt-4" label="Set Past Report" type="submit" color="primary" />
            </div>
          </form>
        </PC.modal>

        <PC.modal
          :if={@show_select_nominees_modal == true}
          id="fetch_nominees-modal"
          show
          on_cancel={JS.push("fetch_nominees_cancel")}
          title="Select nominees to fetch"
        >
          <div class="flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-700 pb-4">
            <label class="inline-flex items-center">
              <input
                type="checkbox"
                phx-click="toggle_select_all_nominees"
                checked={Enum.all?(@nominees, & &1.selected)}
                class="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
              />
              <span class="ml-2 text-sm text-gray-800 dark:text-gray-200 font-semibold">
                Toggle all
              </span>
            </label>
          </div>
          <%= for nominee <- @nominees do %>
            <div class="flex items-center justify-between p-2 border-b border-gray-200 dark:border-gray-700">
              <label class="inline-flex items-center">
                <input
                  type="checkbox"
                  phx-click="toggle_nominee_selection"
                  phx-value-name={nominee.name}
                  checked={nominee.selected}
                  class="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
                />
                <span class="ml-2 text-sm text-gray-800 dark:text-gray-200">
                  {nominee.name}
                </span>
              </label>
            </div>
          <% end %>
          <PC.button
            class="mt-4"
            label="Fetch Nominees"
            phx-click="fetch_nominees"
            color="primary"
            size="sm"
            disabled={Enum.empty?(Enum.filter(@nominees, & &1.selected))}
          />
        </PC.modal>
      </div>
    </div>
    """
  end

  def handle_info({:processing_document_update, message}, socket) do
    case message do
      {:total_chunks, total_chunks} ->
        {:noreply, assign(socket, :total_chunks, total_chunks)}

      {:processed_chunk, processed_chunk} ->
        socket =
          socket
          |> assign(:processed_chunks, processed_chunk + socket.assigns.processed_chunks)
          |> assign(
            :processing_message,
            "Processed #{socket.assigns.processed_chunks} of #{socket.assigns.total_chunks} chunks..."
          )

        {:noreply, socket}

      _ ->
        {:noreply, assign(socket, :processing_message, message)}
    end
  end

  def handle_info({:refresh_candidates}, socket) do
    filter = %{
      status: socket.assigns.status_filter,
      type: socket.assigns.type_filter,
      sent: socket.assigns.sent_filter
    }

    all_report_candidates =
      BeneficialOwners.list_cascaded_beneficial_owner_report_candidates_by_report(
        socket.assigns.report_id,
        filter
      )

    {:noreply, assign(socket, :all_report_candidates, all_report_candidates)}
  end

  def handle_info({:refresh_candidates, :refresh_report}, socket) do
    report_id = String.to_integer(socket.assigns.report_id)
    {_, socket} = handle_info({:refresh_candidates}, socket)

    {:noreply, assign(socket, :report, BeneficialOwners.get_report_by_id!(report_id))}
  end

  def handle_info({:update_status_filter, status}, socket) do
    # Store the filter in the socket assigns for later use
    {:noreply, assign(socket, :status_filter, status)}
  end

  def handle_info({:update_type_filter, type}, socket) do
    # Store the filter in the socket assigns for later use
    {:noreply, assign(socket, :type_filter, type)}
  end

  def handle_info({:update_sent_filter, sent}, socket) do
    # Store the filter in the socket assigns for later use
    {:noreply, assign(socket, :sent_filter, sent)}
  end

  def handle_info({:close_upload_past_placement_report_modal}, socket) do
    {:noreply, assign(socket, :show_upload_past_placement_report_modal, false)}
  end

  def handle_info({:processing_document, :started}, socket) do
    socket =
      assign(socket, :processing_status, :started)

    {:noreply, socket}
  end

  def handle_info({:processing_document, :finished}, socket) do
    socket =
      socket
      |> assign(:processing_status, :ready)
      |> assign(:processing_message, "Great! Please click the button to refresh the candidates")

    {:noreply, socket}
  end

  def handle_info({:flash, type, message}, socket) do
    {:noreply, put_flash(socket, type, message)}
  end

  def handle_info({:update_expanded_parents, expanded_parents}, socket) do
    {:noreply, assign(socket, :expanded_parents, expanded_parents)}
  end

  def handle_event("toggle_nominee_selection", %{"name" => name, "value" => value}, socket) do
    nominees =
      Enum.map(socket.assigns.nominees, fn nominee ->
        if nominee.name == name do
          %{nominee | selected: value == "on"}
        else
          nominee
        end
      end)

    {:noreply, assign(socket, :nominees, nominees)}
  end

  def handle_event("toggle_select_all_nominees", _params, socket) do
    select_all = Enum.all?(socket.assigns.nominees, & &1.selected)

    nominees =
      Enum.map(socket.assigns.nominees, fn nominee ->
        %{nominee | selected: !select_all}
      end)

    {:noreply, assign(socket, :nominees, nominees)}
  end

  def handle_event("fetch_nominees_init", _params, socket) do
    common_nominees = BeneficialOwners.uk_common_nominees()

    socket =
      socket
      |> assign(:show_select_nominees_modal, true)
      |> assign(
        :nominees,
        Enum.map(common_nominees, fn nominee -> %{selected: false, name: nominee} end)
      )

    {:noreply, socket}
  end

  def handle_event("confirm_set_past_report", %{"past_report_date" => past_report_date}, socket) do
    report_id = String.to_integer(socket.assigns.report_id)

    case BeneficialOwners.set_past_report(report_id, past_report_date) do
      {:ok, updated_report} ->
        socket =
          socket
          |> assign(:show_set_past_report_modal, false)
          |> assign(:past_report_date, nil)
          |> assign(:report, updated_report)

        {:noreply, socket}

      {:error, _changeset} ->
        socket = put_flash(socket, :error, "Failed to set past report")
        {:noreply, socket}
    end
  end

  def handle_event("set_past_report", _params, socket) do
    socket =
      assign(socket, :show_set_past_report_modal, true)

    {:noreply, socket}
  end

  def handle_event("set_past_report_cancel", _params, socket) do
    socket =
      assign(socket, :show_set_past_report_modal, false)

    {:noreply, socket}
  end

  def handle_event("fetch_nominees_cancel", _params, socket) do
    socket =
      assign(socket, :show_select_nominees_modal, false)

    {:noreply, socket}
  end

  def handle_event("fetch_nominees", _params, socket) do
    nominees = socket.assigns.nominees |> Enum.filter(& &1.selected) |> Enum.map(& &1.name)

    company_profile_id = socket.assigns.company_profile_id
    report_id = String.to_integer(socket.assigns.report_id)

    BeneficialOwners.create_report_candidates_for_uk(company_profile_id, report_id, nominees)

    # Send a message to refresh the candidates list
    send(self(), {:refresh_candidates})

    {:noreply, assign(socket, :show_select_nominees_modal, false)}
  end

  def handle_event("refresh_candidates", _params, socket) do
    socket =
      assign(socket, :processing_message, nil)

    send(self(), {:refresh_candidates})

    {:noreply, socket}
  end

  def handle_event("fetch_top_50", _params, socket) do
    company_profile_id = socket.assigns.company_profile_id
    report_id = String.to_integer(socket.assigns.report_id)

    report_time =
      if socket.assigns.report.is_past do
        Gaia.Registers.get_latest_daily_holding_date_by_company_profile_and_date(
          company_profile_id,
          socket.assigns.report.report_date
        )
      else
        Gaia.Registers.get_latest_daily_holding_date_by_company_profile(company_profile_id)
      end

    if is_nil(report_time) do
      put_flash(
        socket,
        :error,
        "No shareholding data found for company profile #{company_profile_id}"
      )
    else
      BeneficialOwners.create_report_candidates_from_top_50_shareholders(
        company_profile_id,
        report_id,
        report_time
      )

      # Send a message to refresh the candidates list
      send(self(), {:refresh_candidates})
    end

    {:noreply, socket}
  end

  def handle_event("restart", _params, socket) do
    report_id = String.to_integer(socket.assigns.report_id)

    # Reset the report stage to :processing_stage_1
    {:ok, updated_report} =
      report_id
      |> BeneficialOwners.get_report_by_id!()
      |> BeneficialOwners.update_report(%{stage: :processing_stage_1, sync_at: nil})

    BeneficialOwners.delete_all_candidate_from_report(report_id)

    # Check if the restart was successful
    all_report_candidates =
      BeneficialOwners.list_cascaded_beneficial_owner_report_candidates_by_report(
        report_id,
        :all
      )

    socket =
      case all_report_candidates do
        [] ->
          put_flash(socket, :info, "Successfully restarted")

        _ ->
          put_flash(socket, :info, "Oops, something went wrong")
      end

    {:noreply, socket |> assign(:report, updated_report) |> assign(:all_report_candidates, all_report_candidates)}
  end

  def handle_event("add_top_level", _params, socket) do
    report_id = String.to_integer(socket.assigns.report_id)

    # Create a new top-level candidate
    {:ok, _new_candidate} =
      BeneficialOwners.create_report_candidate_with_nominee_or_asic(%{
        layer: 1,
        report_id: report_id,
        account_name: "",
        shares: 0,
        type: :nominee
      })

    # Send a message to refresh the candidates list
    send(self(), {:refresh_candidates})

    {:noreply, socket}
  end

  def handle_event("navigate", %{"to" => to}, socket) do
    {:noreply, push_navigate(socket, to: to)}
  end

  def handle_event("collapse_all", _params, socket) do
    # Send a message to the table component to collapse all parents
    send_update(HadesWeb.Pages.Organisations.BoReport.ReportCandidate.Table,
      id: "bo-report-candidates-table",
      action: :collapse_all
    )

    {:noreply, socket}
  end

  def handle_event("sync_future_report", _params, socket) do
    report_id = String.to_integer(socket.assigns.report_id)

    BeneficialOwners.sync_future_report(report_id)

    {:noreply, socket}
  end

  def handle_event("go_to_next_stage", _params, socket) do
    company_profile_id = socket.assigns.company_profile_id
    report_id = socket.assigns.report_id

    case BeneficialOwners.go_to_next_stage(company_profile_id, report_id) do
      {:ok, result} ->
        {:ok, updated_report} = Map.get(result, :update_stage, {:ok, %{stage: :processing_stage_1}})

        # Send a message to refresh the candidates list
        send(self(), {:refresh_candidates})

        # Get the number of updated children
        {updated_children_count, _} = Map.get(result, :update_children, {0, nil})

        socket =
          put_flash(
            socket,
            :info,
            "Stage changed to #{if updated_report.stage == :processing_stage_2, do: "2", else: "'done'"}. #{updated_children_count} children updated and #{length(Map.get(result, :add_missing_shareholders, []))} new shareholders added."
          )

        {:noreply, assign(socket, :report, updated_report)}

      {:error, :no_shareholding_data, message, _} ->
        socket = put_flash(socket, :error, message)
        {:noreply, socket}

      {:error, failed_operation, reason, _changes} ->
        socket =
          put_flash(
            socket,
            :error,
            "Failed to move to next stage: #{inspect(failed_operation)} - #{inspect(reason)}"
          )

        {:noreply, socket}
    end
  end

  def handle_event("sync_accounts", _params, socket) do
    report_id = String.to_integer(socket.assigns.report_id)

    case BeneficialOwners.sync_candidates_to_accounts_and_holdings_and_participants(report_id) do
      {:ok, _result} ->
        socket =
          socket
          |> put_flash(:info, "Sync to accounts completed successfully.")
          |> assign(:report, BeneficialOwners.get_report_by_id!(report_id))

        {:noreply, socket}

      {:error, _step, reason, _changes} ->
        socket =
          put_flash(socket, :error, "Sync failed: #{inspect(reason)}")

        {:noreply, socket}
    end
  end

  def handle_event("sync_participants", _params, socket) do
    report_id = String.to_integer(socket.assigns.report_id)

    case BeneficialOwners.sync_candidates_to_participants(report_id) do
      {:ok, _result} ->
        socket =
          socket
          |> put_flash(:info, "Sync participants completed successfully.")
          |> assign(:report, BeneficialOwners.get_report_by_id!(report_id))

        {:noreply, socket}

      {:error, _step, reason, _changes} ->
        socket =
          put_flash(socket, :error, "Sync failed: #{inspect(reason)}")

        {:noreply, socket}
    end
  end

  def handle_event("upload_past_placement_report", _params, socket) do
    # This function will be implemented later
    socket = assign(socket, :show_upload_past_placement_report_modal, true)

    {:noreply, socket}
  end

  def handle_event("upload_past_placement_report_cancel", _params, socket) do
    socket = assign(socket, :show_upload_past_placement_report_modal, false)

    {:noreply, socket}
  end

  def handle_event("update_past_report_date", %{"past_report_date" => date}, socket) do
    {:noreply, assign(socket, :past_report_date, date)}
  end

  def handle_event("edit_shares", _, socket) do
    {:noreply, assign(socket, :editing_report, :total_shares)}
  end

  def handle_event("edit_report_date", _, socket) do
    {:noreply, assign(socket, :editing_report, :report_date)}
  end

  def handle_event("update_report", params, socket) do
    edit_form = socket.assigns.edit_form

    edit_form =
      if Map.has_key?(params, "report_date") do
        # We don't want to update total_shares when updating report_date
        edit_form |> Map.put(:report_date, params["report_date"]) |> Map.delete(:total_shares)
      else
        edit_form
      end

    edit_form =
      if Map.has_key?(params, "total_shares") do
        Map.put(edit_form, :total_shares, params["total_shares"])
      else
        edit_form
      end

    {:noreply, assign(socket, :edit_form, edit_form)}
  end

  def handle_event("prevent_default", _params, socket) do
    {:noreply, socket}
  end

  def handle_event("keydown", %{"key" => "Enter"}, socket), do: save_edits(socket)
  def handle_event("keydown", %{"key" => "Tab"}, socket), do: save_edits(socket)

  def handle_event("keydown", %{"key" => "Escape"}, socket), do: {:noreply, assign(socket, :editing_report, nil)}

  def handle_event("keydown", _params, socket) do
    {:noreply, socket}
  end

  # Private helper function to handle saving edits
  defp save_edits(socket) do
    report_id = String.to_integer(socket.assigns.report_id)
    edit_form = socket.assigns.edit_form
    report = BeneficialOwners.get_report_by_id!(report_id)

    IO.puts("Saving edits for report ID: #{report_id}, edit_form: #{inspect(edit_form)}")

    case BeneficialOwners.update_report(report, edit_form) do
      {:ok, updated_report} ->
        socket =
          socket
          |> assign(:editing_report, nil)
          |> assign(:report, updated_report)
          |> assign(:total_shares_company, Map.get(edit_form, :total_shares, socket.assigns.total_shares_company))

        {:noreply, socket}

      {:error, _changeset} ->
        socket = put_flash(socket, :error, "Failed to update")
        {:noreply, socket}
    end
  end

  defp is_candidates_empty?([]), do: true
  defp is_candidates_empty?(nil), do: true
  defp is_candidates_empty?(_), do: false

  defp melbourne_time(nil), do: nil

  defp melbourne_time(naive_dt) do
    naive_dt
    |> Timex.to_datetime("Etc/UTC")
    |> Timex.Timezone.convert("Australia/Melbourne")
    |> Timex.format!("%Y-%m-%d %H:%M", :strftime)
  end

  defp format_number_with_commas(number) when is_binary(number) do
    number
    |> String.reverse()
    |> String.replace(~r/.{3}(?=.)/, "\\0,")
    |> String.reverse()
  end

  defp format_number_with_commas(number) when is_integer(number) do
    number
    |> Integer.to_string()
    |> format_number_with_commas()
  end

  defp stage_label(:processing_stage_1 = assigns) do
    ~H"""
    <span class="text-orange-500 dark:text-orange-400">
      Unmasking layer 1
    </span>
    """
  end

  defp stage_label(:processing_stage_2 = assigns) do
    ~H"""
    <span class="text-yellow-500 dark:text-yellow-400">
      Unmasking layer 2
    </span>
    """
  end

  defp stage_label(assigns) do
    ~H"""
    <span class="text-green-500 dark:text-green-400">
      Done
    </span>
    """
  end
end
