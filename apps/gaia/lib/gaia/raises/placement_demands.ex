defmodule Gaia.Raises.PlacementDemands do
  @moduledoc """
  The Raises.PlacementDemands context.
  """

  import Ecto.Query, warn: false

  alias Gaia.Markets
  alias Gaia.Raises.PlacementDemands.Shareholding, as: PDShareholding
  alias Gaia.Registers
  alias Gaia.Registers.Broker
  alias Gaia.Repo

  @doc """
  Gets a single shareholding.

  Raises `Ecto.NoResultsError` if the insight does not exist.

  ## Examples

      iex> get_shareholding!(123)
      %PDShareholding{}

      iex> get_shareholding!(456)
      ** (Ecto.NoResultsError)

  """
  def get_shareholding!(id), do: Repo.get!(PDShareholding, id)

  def get_shareholding(id), do: Repo.get(PDShareholding, id)

  def get_shareholding_by(attrs), do: Repo.get_by(PDShareholding, attrs)

  @doc """
  Creates a shareholding.

  ## Examples

      iex> create_shareholding(%{field: value})
      {:ok, %PDShareholding{}}

      iex> create_shareholding(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_shareholding(attrs \\ %{}) do
    %PDShareholding{}
    |> PDShareholding.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a shareholding.

  ## Examples

      iex> update_shareholding(shareholding, %{field: new_value})
      {:ok, %PDShareholding{}}

      iex> update_shareholding(shareholding, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_shareholding(%PDShareholding{} = shareholding, attrs) do
    shareholding
    |> PDShareholding.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Upsert a shareholding.
  """
  def upsert_shareholding(%{shareholding_id: shareholding_id} = attrs) do
    case get_shareholding_by(%{shareholding_id: shareholding_id}) do
      nil ->
        create_shareholding(attrs)

      %PDShareholding{} = existing ->
        update_shareholding(existing, attrs)
    end
  end

  @doc """
  Upsert shareholdings.
  """
  def upsert_shareholdings(shareholdings, company_profile_id, replace_attrs \\ []) do
    Ecto.Multi.new()
    |> Ecto.Multi.insert_all(
      :insert_all,
      PDShareholding,
      Enum.map(
        shareholdings,
        &(&1
          |> Map.delete(:holder_id)
          |> Map.merge(%{
            company_profile_id: company_profile_id,
            inserted_at: NaiveDateTime.utc_now(:second),
            updated_at: NaiveDateTime.utc_now(:second)
          }))
      ),
      on_conflict: {:replace, replace_attrs ++ [:updated_at]},
      conflict_target: [:shareholding_id]
    )
    |> Repo.transaction(timeout: 600_000)
  end

  @doc """
  Deletes a shareholding.

  ## Examples

      iex> delete_shareholding(shareholding)
      {:ok, %PDShareholding{}}

      iex> delete_shareholding(shareholding)
      {:error, %Ecto.Changeset{}}

  """
  def delete_shareholding(%PDShareholding{} = shareholding) do
    Repo.delete(shareholding)
  end

  def list_potential_investors_by_company(company_profile_id) do
    PDShareholding
    |> join(:inner, [shareholding], shareholder_info in assoc(shareholding, :shareholding))
    |> where(
      [shareholding, _],
      shareholding.company_profile_id == ^company_profile_id and
        (shareholding.holding_size_threshold_met or
           shareholding.property_value_threshold_met or
           shareholding.shares_moved_through_wholesale_broker or
           shareholding.single_trade_volume_threshold_met or
           shareholding.soph_in_fresh_equities)
    )
    |> select([_, shareholder_info], shareholder_info)
    |> Repo.all()
    |> Enum.map(
      &%{
        address: Helper.Address.build_one_line_address_string(&1),
        email: &1.email,
        masked_hin: "*******" <> String.slice(&1.holder_id, -4..-1),
        name: &1.account_name,
        phone_number: &1.phone_number
      }
    )
  end

  def generate_register_estimate(%{company_profile_id: company_profile_id, registry_data_status: registry_data_status}) do
    case registry_data_status do
      %{latest_report_date: latest_report_date, state: :api_imported}
      when not is_nil(latest_report_date) ->
        potential_investors = list_potential_investors_by_company(company_profile_id)

        %{
          potential_investor_count: length(potential_investors),
          potential_investors: potential_investors,
          last_updated_date: latest_report_date
        }

      _ ->
        nil
    end
  end

  # Upsert placement_demands_shareholdings based on conditions below:
  # 1. holding_size_threshold_met
  # 2. shares_moved_through_wholesale_broker
  # 3. single_trade_volume_threshold_met
  # 4. TODO: soph_in_fresh_equities
  def upsert_placement_demands_shareholdings(company_profile_id) do
    update_holding_size_threshold_met_and_wholesale_broker(company_profile_id)
    update_single_trade_volume_threshold_met(company_profile_id)
  end

  # 1. If investor's holding_size >= $100,000
  # Update holding_size_threshold_met as true
  # 2. If investor's broker is wholesale
  # Update shares_moved_through_wholesale_broker as true
  def update_holding_size_threshold_met_and_wholesale_broker(company_profile_id) do
    %{close: latest_share_price, currency: currency} =
      Markets.get_latest_timeseries_non_adjusted_by_company_profile_id(company_profile_id)

    Registers.Shareholding
    |> join(:left, [sh], b in Broker, on: sh.broker_pid == b.broker_pid)
    |> where([sh, b], sh.company_profile_id == ^company_profile_id)
    |> select([sh, b], %{
      shareholding_id: sh.id,
      holding_size_threshold_met:
        fragment(
          "(? * ?::double precision * (CASE WHEN ? = 'GBX' THEN 0.01 ELSE 1 END)) >= 100000",
          sh.share_count,
          ^latest_share_price,
          ^currency
        ),
      shares_moved_through_wholesale_broker:
        fragment(
          "CASE WHEN ? THEN TRUE ELSE FALSE END",
          b.is_wholesale
        )
    })
    |> Repo.all(timeout: 300_000)
    |> Enum.chunk_every(5_000)
    |> Enum.each(
      &upsert_shareholdings(&1, company_profile_id, [
        :holding_size_threshold_met,
        :shares_moved_through_wholesale_broker
      ])
    )
  end

  # If investor's single trade >= $50,000
  # Update single_trade_volume_threshold_met as true
  def update_single_trade_volume_threshold_met(company_profile_id) do
    biggest_movement_query =
      Registers.Shareholding
      |> join(
        :inner_lateral,
        [sh],
        sm in fragment(
          "SELECT movement, estimated_price, currency FROM registers_share_movements WHERE shareholding_id = ? ORDER BY movement DESC, id DESC LIMIT 1",
          sh.id
        ),
        on: true
      )
      |> where([sh, sm], sh.company_profile_id == ^company_profile_id)
      |> select([sh, sm], %{
        shareholding_id: sh.id,
        biggest_movement: sm.movement,
        estimated_price: sm.estimated_price,
        currency: sm.currency
      })

    Registers.Shareholding
    |> join(
      :inner,
      [sh],
      sm in subquery(biggest_movement_query),
      on: sh.id == sm.shareholding_id
    )
    |> select([sh, sm], %{
      shareholding_id: sh.id,
      single_trade_volume_threshold_met:
        fragment(
          "(? * ? * (CASE WHEN ? = 'GBX' THEN 0.01 ELSE 1 END))::double precision >= 50000",
          sm.biggest_movement,
          sm.estimated_price,
          sm.currency
        )
    })
    |> Repo.all(timeout: 300_000)
    |> Enum.chunk_every(5_000)
    |> Enum.each(&upsert_shareholdings(&1, company_profile_id, [:single_trade_volume_threshold_met]))
  end
end
